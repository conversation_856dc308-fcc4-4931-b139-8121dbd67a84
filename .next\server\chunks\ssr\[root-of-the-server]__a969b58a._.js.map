{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatFileSize(bytes: number): string {\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  if (bytes === 0) return '0 Bytes'\n  const i = Math.floor(Math.log(bytes) / Math.log(1024))\n  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]\n}\n\nexport function getFileExtension(filename: string): string {\n  return filename.split('.').pop()?.toLowerCase() || ''\n}\n\nexport function getLanguageFromExtension(extension: string): string {\n  const languageMap: Record<string, string> = {\n    'js': 'javascript',\n    'jsx': 'javascript',\n    'ts': 'typescript',\n    'tsx': 'typescript',\n    'py': 'python',\n    'java': 'java',\n    'cpp': 'cpp',\n    'c': 'c',\n    'cs': 'csharp',\n    'php': 'php',\n    'rb': 'ruby',\n    'go': 'go',\n    'rs': 'rust',\n    'swift': 'swift',\n    'kt': 'kotlin',\n    'scala': 'scala',\n    'html': 'html',\n    'css': 'css',\n    'scss': 'scss',\n    'sass': 'sass',\n    'less': 'less',\n    'json': 'json',\n    'xml': 'xml',\n    'yaml': 'yaml',\n    'yml': 'yaml',\n    'md': 'markdown',\n    'sql': 'sql',\n    'sh': 'bash',\n    'bash': 'bash',\n    'zsh': 'zsh',\n    'fish': 'fish',\n  }\n  \n  return languageMap[extension] || 'text'\n}\n\nexport function shouldIndexFile(filename: string): boolean {\n  const extension = getFileExtension(filename)\n  const indexableExtensions = [\n    'js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs',\n    'swift', 'kt', 'scala', 'html', 'css', 'scss', 'sass', 'less', 'json', 'xml',\n    'yaml', 'yml', 'md', 'sql', 'sh', 'bash', 'zsh', 'fish', 'vue', 'svelte'\n  ]\n  \n  const excludePatterns = [\n    'node_modules',\n    '.git',\n    'dist',\n    'build',\n    '.next',\n    'coverage',\n    '.nyc_output',\n    'vendor',\n    '__pycache__',\n    '.pytest_cache',\n    'target',\n    'bin',\n    'obj',\n  ]\n  \n  // Check if file should be excluded\n  for (const pattern of excludePatterns) {\n    if (filename.includes(pattern)) {\n      return false\n    }\n  }\n  \n  return indexableExtensions.includes(extension)\n}\n\nexport function chunkCode(content: string, maxChunkSize: number = 1000): Array<{\n  content: string\n  startLine: number\n  endLine: number\n}> {\n  const lines = content.split('\\n')\n  const chunks: Array<{ content: string; startLine: number; endLine: number }> = []\n  \n  let currentChunk = ''\n  let startLine = 1\n  let currentLine = 1\n  \n  for (const line of lines) {\n    if (currentChunk.length + line.length > maxChunkSize && currentChunk.length > 0) {\n      chunks.push({\n        content: currentChunk.trim(),\n        startLine,\n        endLine: currentLine - 1,\n      })\n      currentChunk = line + '\\n'\n      startLine = currentLine\n    } else {\n      currentChunk += line + '\\n'\n    }\n    currentLine++\n  }\n  \n  if (currentChunk.trim()) {\n    chunks.push({\n      content: currentChunk.trim(),\n      startLine,\n      endLine: currentLine - 1,\n    })\n  }\n  \n  return chunks\n}\n\nexport function extractCodeSymbols(content: string, language: string): string[] {\n  const symbols: string[] = []\n  \n  // Simple regex patterns for different languages\n  const patterns: Record<string, RegExp[]> = {\n    javascript: [\n      /function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /let\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /var\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n    ],\n    typescript: [\n      /function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /interface\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /type\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n    ],\n    python: [\n      /def\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n      /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    ],\n  }\n  \n  const languagePatterns = patterns[language] || patterns.javascript\n  \n  for (const pattern of languagePatterns) {\n    let match\n    while ((match = pattern.exec(content)) !== null) {\n      symbols.push(match[1])\n    }\n  }\n  \n  return [...new Set(symbols)] // Remove duplicates\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;AAC3E;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;AACrD;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,cAAsC;QAC1C,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK;QACL,MAAM;QACN,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,SAAS;QACT,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IAEA,OAAO,WAAW,CAAC,UAAU,IAAI;AACnC;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,MAAM,YAAY,iBAAiB;IACnC,MAAM,sBAAsB;QAC1B;QAAM;QAAO;QAAM;QAAO;QAAM;QAAQ;QAAO;QAAK;QAAM;QAAO;QAAM;QAAM;QAC7E;QAAS;QAAM;QAAS;QAAQ;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QACvE;QAAQ;QAAO;QAAM;QAAO;QAAM;QAAQ;QAAO;QAAQ;QAAO;KACjE;IAED,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,mCAAmC;IACnC,KAAK,MAAM,WAAW,gBAAiB;QACrC,IAAI,SAAS,QAAQ,CAAC,UAAU;YAC9B,OAAO;QACT;IACF;IAEA,OAAO,oBAAoB,QAAQ,CAAC;AACtC;AAEO,SAAS,UAAU,OAAe,EAAE,eAAuB,IAAI;IAKpE,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,SAAyE,EAAE;IAEjF,IAAI,eAAe;IACnB,IAAI,YAAY;IAChB,IAAI,cAAc;IAElB,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,aAAa,MAAM,GAAG,KAAK,MAAM,GAAG,gBAAgB,aAAa,MAAM,GAAG,GAAG;YAC/E,OAAO,IAAI,CAAC;gBACV,SAAS,aAAa,IAAI;gBAC1B;gBACA,SAAS,cAAc;YACzB;YACA,eAAe,OAAO;YACtB,YAAY;QACd,OAAO;YACL,gBAAgB,OAAO;QACzB;QACA;IACF;IAEA,IAAI,aAAa,IAAI,IAAI;QACvB,OAAO,IAAI,CAAC;YACV,SAAS,aAAa,IAAI;YAC1B;YACA,SAAS,cAAc;QACzB;IACF;IAEA,OAAO;AACT;AAEO,SAAS,mBAAmB,OAAe,EAAE,QAAgB;IAClE,MAAM,UAAoB,EAAE;IAE5B,gDAAgD;IAChD,MAAM,WAAqC;QACzC,YAAY;YACV;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;SACD;IACH;IAEA,MAAM,mBAAmB,QAAQ,CAAC,SAAS,IAAI,SAAS,UAAU;IAElE,KAAK,MAAM,WAAW,iBAAkB;QACtC,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;YAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;QACvB;IACF;IAEA,OAAO;WAAI,IAAI,IAAI;KAAS,CAAC,oBAAoB;;AACnD", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/navigation/navbar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Code, Settings, Home, Github } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nexport function Navbar() {\n  const pathname = usePathname()\n\n  const navigation = [\n    { name: 'Projects', href: '/', icon: Home },\n    { name: 'Settings', href: '/settings', icon: Settings },\n  ]\n\n  return (\n    <nav className=\"border-b border-slate-200/50 dark:border-slate-700/50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-slate-900/60 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center gap-8\">\n            <Link href=\"/\" className=\"flex items-center gap-3 group\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200\">\n                <Code className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"font-bold text-xl bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent\">\n                Code Index\n              </span>\n            </Link>\n\n            <div className=\"hidden md:flex items-center gap-1\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                const isActive = pathname === item.href\n\n                return (\n                  <Link key={item.name} href={item.href}>\n                    <Button\n                      variant={isActive ? 'secondary' : 'ghost'}\n                      size=\"sm\"\n                      className={cn(\n                        'flex items-center gap-2 transition-all duration-200',\n                        isActive\n                          ? 'bg-blue-50 dark:bg-blue-950/50 text-blue-700 dark:text-blue-300 shadow-sm'\n                          : 'hover:bg-slate-50 dark:hover:bg-slate-800'\n                      )}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                      {item.name}\n                    </Button>\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-4\">\n            <div className=\"hidden sm:flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span>AI-Powered Analysis</span>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"hidden sm:flex border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                asChild\n              >\n                <a href=\"https://github.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n                  <Github className=\"h-4 w-4 mr-2\" />\n                  GitHub\n                </a>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,mMAAA,CAAA,OAAI;QAAC;QAC1C;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAK,WAAU;kDAAqI;;;;;;;;;;;;0CAKvJ,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAAiB,MAAM,KAAK,IAAI;kDACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,cAAc;4CAClC,MAAK;4CACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uDACA,WACI,8EACA;;8DAGN,8OAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAZH,KAAK,IAAI;;;;;gCAgBxB;;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,8OAAC;wCAAE,MAAK;wCAAqB,QAAO;wCAAS,KAAI;;0DAC/C,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/settings/settings-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { ArrowLeft, Save, Eye, EyeOff, CheckCircle, XCircle } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Navbar } from '@/components/navigation/navbar'\nimport Link from 'next/link'\n\ninterface Provider {\n  name: string\n  displayName: string\n  isConfigured: boolean\n  description: string\n  websiteUrl: string\n}\n\nexport function SettingsPage() {\n  const [providers, setProviders] = useState<Provider[]>([])\n  const [apiKeys, setApiKeys] = useState<Record<string, string>>({})\n  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({})\n  const [isLoading, setIsLoading] = useState(true)\n  const [isSaving, setIsSaving] = useState(false)\n  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')\n\n  useEffect(() => {\n    fetchProviders()\n  }, [])\n\n  const fetchProviders = async () => {\n    try {\n      const response = await fetch('/api/settings/providers')\n      if (response.ok) {\n        const data = await response.json()\n        setProviders(data.providers || [])\n      }\n    } catch (error) {\n      console.error('Error fetching providers:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleApiKeyChange = (provider: string, value: string) => {\n    setApiKeys(prev => ({ ...prev, [provider]: value }))\n  }\n\n  const toggleShowKey = (provider: string) => {\n    setShowKeys(prev => ({ ...prev, [provider]: !prev[provider] }))\n  }\n\n  const saveSettings = async () => {\n    setIsSaving(true)\n    setSaveStatus('idle')\n\n    try {\n      const response = await fetch('/api/settings/providers', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ apiKeys }),\n      })\n\n      if (response.ok) {\n        setSaveStatus('success')\n        fetchProviders() // Refresh provider status\n        setTimeout(() => setSaveStatus('idle'), 3000)\n      } else {\n        setSaveStatus('error')\n      }\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      setSaveStatus('error')\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const getProviderInfo = (name: string) => {\n    const info = {\n      openai: {\n        description: 'GPT-4 and other OpenAI models',\n        websiteUrl: 'https://platform.openai.com/api-keys',\n      },\n      anthropic: {\n        description: 'Claude 3 and other Anthropic models',\n        websiteUrl: 'https://console.anthropic.com/',\n      },\n      google: {\n        description: 'Gemini and other Google AI models',\n        websiteUrl: 'https://makersuite.google.com/app/apikey',\n      },\n      openrouter: {\n        description: 'Access to multiple AI models through one API',\n        websiteUrl: 'https://openrouter.ai/keys',\n      },\n    }\n    return info[name as keyof typeof info] || { description: '', websiteUrl: '' }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p>Loading settings...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Navbar />\n      {/* Header */}\n      <div className=\"border-b\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                  Back\n                </Button>\n              </Link>\n              <div>\n                <h1 className=\"text-3xl font-bold\">Settings</h1>\n                <p className=\"text-muted-foreground mt-1\">\n                  Configure AI providers and other settings\n                </p>\n              </div>\n            </div>\n            <Button onClick={saveSettings} disabled={isSaving}>\n              {isSaving ? (\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              ) : (\n                <Save className=\"w-4 h-4 mr-2\" />\n              )}\n              Save Settings\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-4xl mx-auto space-y-8\">\n          {/* Save Status */}\n          {saveStatus !== 'idle' && (\n            <div className={`flex items-center gap-2 p-4 rounded-lg ${\n              saveStatus === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'\n            }`}>\n              {saveStatus === 'success' ? (\n                <CheckCircle className=\"w-5 h-5\" />\n              ) : (\n                <XCircle className=\"w-5 h-5\" />\n              )}\n              <span>\n                {saveStatus === 'success' \n                  ? 'Settings saved successfully!' \n                  : 'Failed to save settings. Please try again.'\n                }\n              </span>\n            </div>\n          )}\n\n          {/* AI Providers */}\n          <Card>\n            <CardHeader>\n              <CardTitle>AI Providers</CardTitle>\n              <CardDescription>\n                Configure API keys for different AI providers. You need at least one configured provider to use the chat feature.\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {providers.map((provider) => {\n                const info = getProviderInfo(provider.name)\n                return (\n                  <div key={provider.name} className=\"border rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center gap-3\">\n                        <h3 className=\"font-semibold\">{provider.displayName}</h3>\n                        <Badge variant={provider.isConfigured ? 'default' : 'secondary'}>\n                          {provider.isConfigured ? 'Configured' : 'Not Configured'}\n                        </Badge>\n                      </div>\n                      <a\n                        href={info.websiteUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-sm text-blue-600 hover:underline\"\n                      >\n                        Get API Key →\n                      </a>\n                    </div>\n                    \n                    <p className=\"text-sm text-muted-foreground mb-3\">\n                      {info.description}\n                    </p>\n\n                    <div className=\"flex gap-2\">\n                      <div className=\"flex-1 relative\">\n                        <Input\n                          type={showKeys[provider.name] ? 'text' : 'password'}\n                          placeholder={`Enter ${provider.displayName} API key`}\n                          value={apiKeys[provider.name] || ''}\n                          onChange={(e) => handleApiKeyChange(provider.name, e.target.value)}\n                        />\n                      </div>\n                      <Button\n                        type=\"button\"\n                        variant=\"outline\"\n                        size=\"icon\"\n                        onClick={() => toggleShowKey(provider.name)}\n                      >\n                        {showKeys[provider.name] ? (\n                          <EyeOff className=\"w-4 h-4\" />\n                        ) : (\n                          <Eye className=\"w-4 h-4\" />\n                        )}\n                      </Button>\n                    </div>\n                  </div>\n                )\n              })}\n            </CardContent>\n          </Card>\n\n          {/* Vector Database */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Vector Database</CardTitle>\n              <CardDescription>\n                Configure Upstash Vector for code indexing and search\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"text-sm font-medium\">Upstash Vector REST URL</label>\n                  <Input\n                    placeholder=\"https://your-vector-db.upstash.io\"\n                    className=\"mt-1\"\n                  />\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium\">Upstash Vector REST Token</label>\n                  <Input\n                    type=\"password\"\n                    placeholder=\"Your vector database token\"\n                    className=\"mt-1\"\n                  />\n                </div>\n                <p className=\"text-sm text-muted-foreground\">\n                  Get your Upstash Vector credentials from{' '}\n                  <a\n                    href=\"https://console.upstash.com/\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-blue-600 hover:underline\"\n                  >\n                    Upstash Console →\n                  </a>\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAmBO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAE3E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa,KAAK,SAAS,IAAI,EAAE;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE;YAAM,CAAC;IACpD;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;YAAC,CAAC;IAC/D;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc;gBACd,iBAAiB,0BAA0B;;gBAC3C,WAAW,IAAM,cAAc,SAAS;YAC1C,OAAO;gBACL,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,cAAc;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO;YACX,QAAQ;gBACN,aAAa;gBACb,YAAY;YACd;YACA,WAAW;gBACT,aAAa;gBACb,YAAY;YACd;YACA,QAAQ;gBACN,aAAa;gBACb,YAAY;YACd;YACA,YAAY;gBACV,aAAa;gBACb,YAAY;YACd;QACF;QACA,OAAO,IAAI,CAAC,KAA0B,IAAI;YAAE,aAAa;YAAI,YAAY;QAAG;IAC9E;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI1C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAK9C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,UAAU;;oCACtC,yBACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAChB;;;;;;;;;;;;;;;;;;;;;;;0BAQV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,eAAe,wBACd,8OAAC;4BAAI,WAAW,CAAC,uCAAuC,EACtD,eAAe,YAAY,+BAA+B,0BAC1D;;gCACC,eAAe,0BACd,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,8OAAC,4MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CAErB,8OAAC;8CACE,eAAe,YACZ,iCACA;;;;;;;;;;;;sCAOV,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,UAAU,GAAG,CAAC,CAAC;wCACd,MAAM,OAAO,gBAAgB,SAAS,IAAI;wCAC1C,qBACE,8OAAC;4CAAwB,WAAU;;8DACjC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAiB,SAAS,WAAW;;;;;;8EACnD,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAS,SAAS,YAAY,GAAG,YAAY;8EACjD,SAAS,YAAY,GAAG,eAAe;;;;;;;;;;;;sEAG5C,8OAAC;4DACC,MAAM,KAAK,UAAU;4DACrB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEACX;;;;;;;;;;;;8DAKH,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAGnB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAM,QAAQ,CAAC,SAAS,IAAI,CAAC,GAAG,SAAS;gEACzC,aAAa,CAAC,MAAM,EAAE,SAAS,WAAW,CAAC,QAAQ,CAAC;gEACpD,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI;gEACjC,UAAU,CAAC,IAAM,mBAAmB,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;sEAGrE,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,cAAc,SAAS,IAAI;sEAEzC,QAAQ,CAAC,SAAS,IAAI,CAAC,iBACtB,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;2CAxCb,SAAS,IAAI;;;;;oCA8C3B;;;;;;;;;;;;sCAKJ,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,8OAAC;gDAAE,WAAU;;oDAAgC;oDACF;kEACzC,8OAAC;wDACC,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}