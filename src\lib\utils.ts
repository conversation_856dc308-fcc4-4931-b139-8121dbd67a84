import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import * as path from "path";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// File extension utilities
export function getFileExtension(filePath: string): string {
  return path.extname(filePath).toLowerCase();
}

export function getLanguageFromExtension(extension: string): string {
  const languageMap: Record<string, string> = {
    ".js": "javascript",
    ".jsx": "javascript",
    ".ts": "typescript",
    ".tsx": "typescript",
    ".py": "python",
    ".java": "java",
    ".cpp": "cpp",
    ".c": "c",
    ".cs": "csharp",
    ".php": "php",
    ".rb": "ruby",
    ".go": "go",
    ".rs": "rust",
    ".swift": "swift",
    ".kt": "kotlin",
    ".scala": "scala",
    ".sh": "bash",
    ".sql": "sql",
    ".html": "html",
    ".css": "css",
    ".scss": "scss",
    ".sass": "sass",
    ".less": "less",
    ".vue": "vue",
    ".svelte": "svelte",
    ".md": "markdown",
    ".json": "json",
    ".xml": "xml",
    ".yaml": "yaml",
    ".yml": "yaml",
    ".toml": "toml",
    ".ini": "ini",
    ".cfg": "ini",
    ".conf": "ini",
  };

  return languageMap[extension] || "text";
}

// File filtering
export function shouldIndexFile(filePath: string): boolean {
  const extension = getFileExtension(filePath);
  const fileName = path.basename(filePath);

  // Skip common non-code files
  const skipExtensions = [
    ".png",
    ".jpg",
    ".jpeg",
    ".gif",
    ".svg",
    ".ico",
    ".pdf",
    ".doc",
    ".docx",
    ".xls",
    ".xlsx",
    ".zip",
    ".tar",
    ".gz",
    ".rar",
    ".mp3",
    ".mp4",
    ".avi",
    ".mov",
    ".exe",
    ".dll",
    ".so",
    ".dylib",
    ".log",
    ".tmp",
    ".cache",
    ".lock",
    ".pid",
  ];

  // Skip common directories and files
  const skipPatterns = [
    "node_modules",
    ".git",
    ".next",
    ".nuxt",
    "dist",
    "build",
    "coverage",
    ".nyc_output",
    "vendor",
    "__pycache__",
    ".pytest_cache",
    ".vscode",
    ".idea",
    "package-lock.json",
    "yarn.lock",
    "pnpm-lock.yaml",
    ".env",
    ".env.local",
    ".env.production",
    ".DS_Store",
    "Thumbs.db",
  ];

  // Check if file should be skipped
  if (skipExtensions.includes(extension)) {
    return false;
  }

  // Check if path contains skip patterns
  for (const pattern of skipPatterns) {
    if (filePath.includes(pattern)) {
      return false;
    }
  }

  // Only index files with known extensions or common code files
  const codeExtensions = [
    ".js",
    ".jsx",
    ".ts",
    ".tsx",
    ".vue",
    ".svelte",
    ".py",
    ".java",
    ".cpp",
    ".c",
    ".cs",
    ".php",
    ".rb",
    ".go",
    ".rs",
    ".swift",
    ".kt",
    ".scala",
    ".sh",
    ".sql",
    ".html",
    ".css",
    ".scss",
    ".sass",
    ".less",
    ".md",
    ".json",
    ".xml",
    ".yaml",
    ".yml",
    ".toml",
    ".ini",
    ".cfg",
    ".conf",
    ".txt",
    ".readme",
  ];

  return (
    codeExtensions.includes(extension) ||
    fileName.toLowerCase().includes("readme")
  );
}

// Code chunking
export function chunkCode(
  content: string,
  maxChunkSize: number = 1000
): string[] {
  const lines = content.split("\n");
  const chunks: string[] = [];
  let currentChunk = "";

  for (const line of lines) {
    if (
      currentChunk.length + line.length + 1 > maxChunkSize &&
      currentChunk.length > 0
    ) {
      chunks.push(currentChunk.trim());
      currentChunk = line;
    } else {
      currentChunk += (currentChunk ? "\n" : "") + line;
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return chunks.filter((chunk) => chunk.length > 0);
}

// Code symbol extraction
export function extractCodeSymbols(
  content: string,
  language: string
): string[] {
  const symbols: string[] = [];

  try {
    switch (language) {
      case "javascript":
      case "typescript":
        // Extract function names, class names, etc.
        const jsPatterns = [
          /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
          /class\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
          /const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g,
          /let\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g,
          /var\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g,
          /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*function/g,
          /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>\s*/g,
        ];

        for (const pattern of jsPatterns) {
          let match;
          while ((match = pattern.exec(content)) !== null) {
            symbols.push(match[1]);
          }
        }
        break;

      case "python":
        // Extract Python function and class names
        const pyPatterns = [
          /def\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
          /class\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
        ];

        for (const pattern of pyPatterns) {
          let match;
          while ((match = pattern.exec(content)) !== null) {
            symbols.push(match[1]);
          }
        }
        break;

      case "java":
        // Extract Java method and class names
        const javaPatterns = [
          /class\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
          /interface\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
          /public\s+\w+\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
          /private\s+\w+\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
          /protected\s+\w+\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
        ];

        for (const pattern of javaPatterns) {
          let match;
          while ((match = pattern.exec(content)) !== null) {
            symbols.push(match[1]);
          }
        }
        break;
    }
  } catch (error) {
    console.error("Error extracting symbols:", error);
  }

  return [...new Set(symbols)]; // Remove duplicates
}

// File size formatting
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
