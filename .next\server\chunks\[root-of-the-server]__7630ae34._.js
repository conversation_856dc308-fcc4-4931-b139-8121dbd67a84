module.exports = {

"[project]/.next-internal/server/app/api/settings/providers/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/ai-providers/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OpenAIProvider": (()=>OpenAIProvider)
});
class OpenAIProvider {
    name = 'openai';
    displayName = 'OpenAI';
    config;
    constructor(config){
        this.config = {
            model: 'gpt-4',
            maxTokens: 4000,
            temperature: 0.7,
            ...config
        };
    }
    async chat(messages, functions) {
        if (!this.isConfigured()) {
            throw new Error('OpenAI API key not configured');
        }
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: this.config.model,
                messages: messages.map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                max_tokens: this.config.maxTokens,
                temperature: this.config.temperature,
                functions: functions?.map((fn)=>({
                        name: fn.name,
                        description: fn.description,
                        parameters: fn.parameters
                    }))
            })
        });
        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.statusText}`);
        }
        const data = await response.json();
        const choice = data.choices[0];
        return {
            content: choice.message.content || '',
            functionCalls: choice.message.function_call ? [
                {
                    name: choice.message.function_call.name,
                    arguments: JSON.parse(choice.message.function_call.arguments)
                }
            ] : undefined,
            usage: data.usage ? {
                promptTokens: data.usage.prompt_tokens,
                completionTokens: data.usage.completion_tokens,
                totalTokens: data.usage.total_tokens
            } : undefined
        };
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
}
}}),
"[project]/src/lib/ai-providers/anthropic.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AnthropicProvider": (()=>AnthropicProvider)
});
class AnthropicProvider {
    name = 'anthropic';
    displayName = 'Anthropic';
    config;
    constructor(config){
        this.config = {
            model: 'claude-3-sonnet-20240229',
            maxTokens: 4000,
            temperature: 0.7,
            ...config
        };
    }
    async chat(messages, functions) {
        if (!this.isConfigured()) {
            throw new Error('Anthropic API key not configured');
        }
        const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'x-api-key': this.config.apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: this.config.model,
                max_tokens: this.config.maxTokens,
                temperature: this.config.temperature,
                messages: messages.filter((msg)=>msg.role !== 'system').map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                system: messages.find((msg)=>msg.role === 'system')?.content
            })
        });
        if (!response.ok) {
            throw new Error(`Anthropic API error: ${response.statusText}`);
        }
        const data = await response.json();
        return {
            content: data.content[0]?.text || '',
            usage: data.usage ? {
                promptTokens: data.usage.input_tokens,
                completionTokens: data.usage.output_tokens,
                totalTokens: data.usage.input_tokens + data.usage.output_tokens
            } : undefined
        };
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
}
}}),
"[project]/src/lib/ai-providers/google.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GoogleProvider": (()=>GoogleProvider)
});
class GoogleProvider {
    name = 'google';
    displayName = 'Google AI';
    config;
    constructor(config){
        this.config = {
            model: 'gemini-pro',
            maxTokens: 4000,
            temperature: 0.7,
            baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
            ...config
        };
    }
    async chat(messages, functions) {
        if (!this.isConfigured()) {
            throw new Error('Google AI API key not configured');
        }
        // Convert messages to Google's format
        const contents = messages.map((msg)=>({
                role: msg.role === 'assistant' ? 'model' : 'user',
                parts: [
                    {
                        text: msg.content
                    }
                ]
            }));
        const response = await fetch(`${this.config.baseUrl}/models/${this.config.model}:generateContent?key=${this.config.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents,
                generationConfig: {
                    temperature: this.config.temperature,
                    maxOutputTokens: this.config.maxTokens
                }
            })
        });
        if (!response.ok) {
            throw new Error(`Google AI API error: ${response.statusText}`);
        }
        const data = await response.json();
        const candidate = data.candidates?.[0];
        if (!candidate) {
            throw new Error('No response from Google AI');
        }
        return {
            content: candidate.content?.parts?.[0]?.text || '',
            usage: data.usageMetadata ? {
                promptTokens: data.usageMetadata.promptTokenCount || 0,
                completionTokens: data.usageMetadata.candidatesTokenCount || 0,
                totalTokens: data.usageMetadata.totalTokenCount || 0
            } : undefined
        };
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
}
}}),
"[project]/src/lib/ai-providers/openrouter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OpenRouterProvider": (()=>OpenRouterProvider)
});
class OpenRouterProvider {
    name = 'openrouter';
    displayName = 'OpenRouter';
    config;
    constructor(config){
        this.config = {
            model: 'anthropic/claude-3-sonnet',
            maxTokens: 4000,
            temperature: 0.7,
            baseUrl: 'https://openrouter.ai/api/v1',
            ...config
        };
    }
    async chat(messages, functions) {
        if (!this.isConfigured()) {
            throw new Error('OpenRouter API key not configured');
        }
        const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:3000',
                'X-Title': 'Code Index'
            },
            body: JSON.stringify({
                model: this.config.model,
                messages: messages.map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                max_tokens: this.config.maxTokens,
                temperature: this.config.temperature,
                functions: functions?.map((fn)=>({
                        name: fn.name,
                        description: fn.description,
                        parameters: fn.parameters
                    }))
            })
        });
        if (!response.ok) {
            throw new Error(`OpenRouter API error: ${response.statusText}`);
        }
        const data = await response.json();
        const choice = data.choices[0];
        return {
            content: choice.message.content || '',
            functionCalls: choice.message.function_call ? [
                {
                    name: choice.message.function_call.name,
                    arguments: JSON.parse(choice.message.function_call.arguments)
                }
            ] : undefined,
            usage: data.usage ? {
                promptTokens: data.usage.prompt_tokens,
                completionTokens: data.usage.completion_tokens,
                totalTokens: data.usage.total_tokens
            } : undefined
        };
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
}
}}),
"[project]/src/lib/ai-providers/types.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
}}),
"[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createProvider": (()=>createProvider),
    "getAvailableProviders": (()=>getAvailableProviders),
    "getConfiguredProviders": (()=>getConfiguredProviders),
    "getProviderConfig": (()=>getProviderConfig)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$anthropic$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/anthropic.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$google$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/google.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openrouter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/openrouter.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$types$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/types.ts [app-route] (ecmascript)");
;
;
;
;
;
function createProvider(name, config) {
    switch(name){
        case "openai":
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenAIProvider"](config);
        case "anthropic":
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$anthropic$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AnthropicProvider"](config);
        case "google":
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$google$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleProvider"](config);
        case "openrouter":
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openrouter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenRouterProvider"](config);
        default:
            throw new Error(`Unknown provider: ${name}`);
    }
}
function getAvailableProviders() {
    return [
        {
            name: "openai",
            displayName: "OpenAI"
        },
        {
            name: "anthropic",
            displayName: "Anthropic"
        },
        {
            name: "google",
            displayName: "Google AI"
        },
        {
            name: "openrouter",
            displayName: "OpenRouter"
        }
    ];
}
function getConfiguredProviders() {
    const providers = getAvailableProviders();
    return providers.map((provider)=>{
        let isConfigured = false;
        try {
            const config = getProviderConfig(provider.name);
            const providerInstance = createProvider(provider.name, config);
            isConfigured = providerInstance.isConfigured();
        } catch (error) {
        // Provider not configured
        }
        return {
            ...provider,
            isConfigured
        };
    });
}
function getProviderConfig(name) {
    switch(name){
        case "openai":
            return {
                apiKey: process.env.OPENAI_API_KEY
            };
        case "anthropic":
            return {
                apiKey: process.env.ANTHROPIC_API_KEY
            };
        case "google":
            return {
                apiKey: process.env.GOOGLE_API_KEY
            };
        case "openrouter":
            return {
                apiKey: process.env.OPENROUTER_API_KEY
            };
        default:
            throw new Error(`Unknown provider: ${name}`);
    }
}
}}),
"[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$anthropic$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/anthropic.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$google$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/google.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openrouter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/openrouter.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$types$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/types.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <locals>");
}}),
"[project]/src/app/api/settings/providers/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <locals>");
;
;
async function GET() {
    try {
        const providers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getConfiguredProviders"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            providers
        });
    } catch (error) {
        console.error('Error fetching providers:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch providers'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { apiKeys } = body;
        // In a real application, you would save these to a secure database
        // For this demo, we'll just return success
        // The API keys should be stored encrypted and associated with the user
        console.log('API keys would be saved:', Object.keys(apiKeys));
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Settings saved successfully'
        });
    } catch (error) {
        console.error('Error saving settings:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to save settings'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7630ae34._.js.map