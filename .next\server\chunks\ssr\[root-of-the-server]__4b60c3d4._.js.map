{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatFileSize(bytes: number): string {\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  if (bytes === 0) return '0 Bytes'\n  const i = Math.floor(Math.log(bytes) / Math.log(1024))\n  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]\n}\n\nexport function getFileExtension(filename: string): string {\n  return filename.split('.').pop()?.toLowerCase() || ''\n}\n\nexport function getLanguageFromExtension(extension: string): string {\n  const languageMap: Record<string, string> = {\n    'js': 'javascript',\n    'jsx': 'javascript',\n    'ts': 'typescript',\n    'tsx': 'typescript',\n    'py': 'python',\n    'java': 'java',\n    'cpp': 'cpp',\n    'c': 'c',\n    'cs': 'csharp',\n    'php': 'php',\n    'rb': 'ruby',\n    'go': 'go',\n    'rs': 'rust',\n    'swift': 'swift',\n    'kt': 'kotlin',\n    'scala': 'scala',\n    'html': 'html',\n    'css': 'css',\n    'scss': 'scss',\n    'sass': 'sass',\n    'less': 'less',\n    'json': 'json',\n    'xml': 'xml',\n    'yaml': 'yaml',\n    'yml': 'yaml',\n    'md': 'markdown',\n    'sql': 'sql',\n    'sh': 'bash',\n    'bash': 'bash',\n    'zsh': 'zsh',\n    'fish': 'fish',\n  }\n  \n  return languageMap[extension] || 'text'\n}\n\nexport function shouldIndexFile(filename: string): boolean {\n  const extension = getFileExtension(filename)\n  const indexableExtensions = [\n    'js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs',\n    'swift', 'kt', 'scala', 'html', 'css', 'scss', 'sass', 'less', 'json', 'xml',\n    'yaml', 'yml', 'md', 'sql', 'sh', 'bash', 'zsh', 'fish', 'vue', 'svelte'\n  ]\n  \n  const excludePatterns = [\n    'node_modules',\n    '.git',\n    'dist',\n    'build',\n    '.next',\n    'coverage',\n    '.nyc_output',\n    'vendor',\n    '__pycache__',\n    '.pytest_cache',\n    'target',\n    'bin',\n    'obj',\n  ]\n  \n  // Check if file should be excluded\n  for (const pattern of excludePatterns) {\n    if (filename.includes(pattern)) {\n      return false\n    }\n  }\n  \n  return indexableExtensions.includes(extension)\n}\n\nexport function chunkCode(content: string, maxChunkSize: number = 1000): Array<{\n  content: string\n  startLine: number\n  endLine: number\n}> {\n  const lines = content.split('\\n')\n  const chunks: Array<{ content: string; startLine: number; endLine: number }> = []\n  \n  let currentChunk = ''\n  let startLine = 1\n  let currentLine = 1\n  \n  for (const line of lines) {\n    if (currentChunk.length + line.length > maxChunkSize && currentChunk.length > 0) {\n      chunks.push({\n        content: currentChunk.trim(),\n        startLine,\n        endLine: currentLine - 1,\n      })\n      currentChunk = line + '\\n'\n      startLine = currentLine\n    } else {\n      currentChunk += line + '\\n'\n    }\n    currentLine++\n  }\n  \n  if (currentChunk.trim()) {\n    chunks.push({\n      content: currentChunk.trim(),\n      startLine,\n      endLine: currentLine - 1,\n    })\n  }\n  \n  return chunks\n}\n\nexport function extractCodeSymbols(content: string, language: string): string[] {\n  const symbols: string[] = []\n  \n  // Simple regex patterns for different languages\n  const patterns: Record<string, RegExp[]> = {\n    javascript: [\n      /function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /let\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /var\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n    ],\n    typescript: [\n      /function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /interface\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /type\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n      /const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n    ],\n    python: [\n      /def\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n      /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n    ],\n  }\n  \n  const languagePatterns = patterns[language] || patterns.javascript\n  \n  for (const pattern of languagePatterns) {\n    let match\n    while ((match = pattern.exec(content)) !== null) {\n      symbols.push(match[1])\n    }\n  }\n  \n  return [...new Set(symbols)] // Remove duplicates\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;AAC3E;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;AACrD;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,cAAsC;QAC1C,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,OAAO;QACP,KAAK;QACL,MAAM;QACN,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;QACN,SAAS;QACT,MAAM;QACN,SAAS;QACT,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IAEA,OAAO,WAAW,CAAC,UAAU,IAAI;AACnC;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,MAAM,YAAY,iBAAiB;IACnC,MAAM,sBAAsB;QAC1B;QAAM;QAAO;QAAM;QAAO;QAAM;QAAQ;QAAO;QAAK;QAAM;QAAO;QAAM;QAAM;QAC7E;QAAS;QAAM;QAAS;QAAQ;QAAO;QAAQ;QAAQ;QAAQ;QAAQ;QACvE;QAAQ;QAAO;QAAM;QAAO;QAAM;QAAQ;QAAO;QAAQ;QAAO;KACjE;IAED,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,mCAAmC;IACnC,KAAK,MAAM,WAAW,gBAAiB;QACrC,IAAI,SAAS,QAAQ,CAAC,UAAU;YAC9B,OAAO;QACT;IACF;IAEA,OAAO,oBAAoB,QAAQ,CAAC;AACtC;AAEO,SAAS,UAAU,OAAe,EAAE,eAAuB,IAAI;IAKpE,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,SAAyE,EAAE;IAEjF,IAAI,eAAe;IACnB,IAAI,YAAY;IAChB,IAAI,cAAc;IAElB,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,aAAa,MAAM,GAAG,KAAK,MAAM,GAAG,gBAAgB,aAAa,MAAM,GAAG,GAAG;YAC/E,OAAO,IAAI,CAAC;gBACV,SAAS,aAAa,IAAI;gBAC1B;gBACA,SAAS,cAAc;YACzB;YACA,eAAe,OAAO;YACtB,YAAY;QACd,OAAO;YACL,gBAAgB,OAAO;QACzB;QACA;IACF;IAEA,IAAI,aAAa,IAAI,IAAI;QACvB,OAAO,IAAI,CAAC;YACV,SAAS,aAAa,IAAI;YAC1B;YACA,SAAS,cAAc;QACzB;IACF;IAEA,OAAO;AACT;AAEO,SAAS,mBAAmB,OAAe,EAAE,QAAgB;IAClE,MAAM,UAAoB,EAAE;IAE5B,gDAAgD;IAChD,MAAM,WAAqC;QACzC,YAAY;YACV;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YACV;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;SACD;IACH;IAEA,MAAM,mBAAmB,QAAQ,CAAC,SAAS,IAAI,SAAS,UAAU;IAElE,KAAK,MAAM,WAAW,iBAAkB;QACtC,IAAI;QACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;YAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;QACvB;IACF;IAEA,OAAO;WAAI,IAAI,IAAI;KAAS,CAAC,oBAAoB;;AACnD", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/navigation/navbar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Code, Settings, Home, Github } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nexport function Navbar() {\n  const pathname = usePathname()\n\n  const navigation = [\n    { name: 'Projects', href: '/', icon: Home },\n    { name: 'Settings', href: '/settings', icon: Settings },\n  ]\n\n  return (\n    <nav className=\"border-b border-slate-200/50 dark:border-slate-700/50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-slate-900/60 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center gap-8\">\n            <Link href=\"/\" className=\"flex items-center gap-3 group\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200\">\n                <Code className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"font-bold text-xl bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent\">\n                Code Index\n              </span>\n            </Link>\n\n            <div className=\"hidden md:flex items-center gap-1\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                const isActive = pathname === item.href\n\n                return (\n                  <Link key={item.name} href={item.href}>\n                    <Button\n                      variant={isActive ? 'secondary' : 'ghost'}\n                      size=\"sm\"\n                      className={cn(\n                        'flex items-center gap-2 transition-all duration-200',\n                        isActive\n                          ? 'bg-blue-50 dark:bg-blue-950/50 text-blue-700 dark:text-blue-300 shadow-sm'\n                          : 'hover:bg-slate-50 dark:hover:bg-slate-800'\n                      )}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                      {item.name}\n                    </Button>\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-4\">\n            <div className=\"hidden sm:flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span>AI-Powered Analysis</span>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"hidden sm:flex border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                asChild\n              >\n                <a href=\"https://github.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n                  <Github className=\"h-4 w-4 mr-2\" />\n                  GitHub\n                </a>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,mMAAA,CAAA,OAAI;QAAC;QAC1C;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAK,WAAU;kDAAqI;;;;;;;;;;;;0CAKvJ,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAAiB,MAAM,KAAK,IAAI;kDACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,cAAc;4CAClC,MAAK;4CACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uDACA,WACI,8EACA;;8DAGN,8OAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAZH,KAAK,IAAI;;;;;gCAgBxB;;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,8OAAC;wCAAE,MAAK;wCAAqB,QAAO;wCAAS,KAAI;;0DAC/C,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/projects/projects-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Search, Code, Clock, FileText, Zap, Brain, Database, Sparkles, ArrowRight, Github, Star, Users, Rocket } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { CreateProjectDialog } from './create-project-dialog'\nimport { Navbar } from '@/components/navigation/navbar'\n\ninterface Project {\n  id: string\n  name: string\n  description?: string\n  path: string\n  language?: string\n  framework?: string\n  isIndexed: boolean\n  indexingStatus: string\n  totalFiles: number\n  indexedFiles: number\n  totalLines: number\n  createdAt: string\n  lastIndexedAt?: string\n}\n\nexport function ProjectsPage() {\n  const [projects, setProjects] = useState<Project[]>([])\n  const [searchQuery, setSearchQuery] = useState('')\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    fetchProjects()\n  }, [])\n\n  const fetchProjects = async () => {\n    try {\n      const response = await fetch('/api/projects')\n      if (response.ok) {\n        const data = await response.json()\n        setProjects(data.projects || [])\n      }\n    } catch (error) {\n      console.error('Error fetching projects:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const filteredProjects = projects.filter(project =>\n    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    project.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    project.language?.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'bg-green-500'\n      case 'indexing': return 'bg-blue-500'\n      case 'failed': return 'bg-red-500'\n      default: return 'bg-gray-500'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed': return 'Indexed'\n      case 'indexing': return 'Indexing...'\n      case 'failed': return 'Failed'\n      default: return 'Pending'\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800\">\n      <Navbar />\n\n      {/* Hero Section */}\n      {projects.length === 0 && !isLoading ? (\n        <div className=\"relative overflow-hidden\">\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]\" />\n\n          <div className=\"relative container mx-auto px-4 py-20\">\n            <div className=\"text-center max-w-4xl mx-auto\">\n              {/* Hero Content */}\n              <div className=\"mb-8\">\n                <div className=\"inline-flex items-center gap-2 bg-blue-50 dark:bg-blue-950/50 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-full text-sm font-medium mb-6\">\n                  <Sparkles className=\"w-4 h-4\" />\n                  AI-Powered Code Analysis\n                </div>\n\n                <h1 className=\"text-5xl md:text-6xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 dark:from-slate-100 dark:via-blue-100 dark:to-slate-100 bg-clip-text text-transparent mb-6\">\n                  Understand Your\n                  <br />\n                  <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\">\n                    Codebase Instantly\n                  </span>\n                </h1>\n\n                <p className=\"text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-2xl mx-auto leading-relaxed\">\n                  Index your code projects and chat with AI to understand functions, classes, and architecture.\n                  Get instant insights about your codebase with semantic search and intelligent analysis.\n                </p>\n\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n                  <Button\n                    size=\"lg\"\n                    className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200\"\n                    onClick={() => setIsCreateDialogOpen(true)}\n                  >\n                    <Plus className=\"w-5 h-5 mr-2\" />\n                    Create Your First Project\n                  </Button>\n                  <Button\n                    size=\"lg\"\n                    variant=\"outline\"\n                    className=\"px-8 py-3 text-lg font-semibold border-2 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                  >\n                    <Github className=\"w-5 h-5 mr-2\" />\n                    View on GitHub\n                  </Button>\n                </div>\n              </div>\n\n              {/* Features Grid */}\n              <div className=\"grid md:grid-cols-3 gap-8 mt-16\">\n                <div className=\"bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-lg transition-all duration-200\">\n                  <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center mb-4\">\n                    <Brain className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold mb-2\">AI-Powered Chat</h3>\n                  <p className=\"text-slate-600 dark:text-slate-300\">\n                    Ask questions about your code and get intelligent responses from multiple AI providers.\n                  </p>\n                </div>\n\n                <div className=\"bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-lg transition-all duration-200\">\n                  <div className=\"w-12 h-12 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center mb-4\">\n                    <Database className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold mb-2\">Smart Indexing</h3>\n                  <p className=\"text-slate-600 dark:text-slate-300\">\n                    Automatically index your codebase with vector embeddings for semantic search.\n                  </p>\n                </div>\n\n                <div className=\"bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm rounded-2xl p-6 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-lg transition-all duration-200\">\n                  <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900/50 rounded-xl flex items-center justify-center mb-4\">\n                    <Rocket className=\"w-6 h-6 text-green-600 dark:text-green-400\" />\n                  </div>\n                  <h3 className=\"text-lg font-semibold mb-2\">Multi-Language</h3>\n                  <p className=\"text-slate-600 dark:text-slate-300\">\n                    Support for 20+ programming languages including JavaScript, Python, Java, and more.\n                  </p>\n                </div>\n              </div>\n\n              {/* Stats */}\n              <div className=\"flex justify-center items-center gap-8 mt-16 text-sm text-slate-500 dark:text-slate-400\">\n                <div className=\"flex items-center gap-2\">\n                  <Star className=\"w-4 h-4\" />\n                  <span>Open Source</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Users className=\"w-4 h-4\" />\n                  <span>Community Driven</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Code className=\"w-4 h-4\" />\n                  <span>Developer First</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      ) : (\n        /* Projects Section */\n        <div className=\"container mx-auto px-4 py-8\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between mb-8\">\n            <div>\n              <h1 className=\"text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent\">\n                Your Projects\n              </h1>\n              <p className=\"text-slate-600 dark:text-slate-400 mt-1\">\n                Manage and analyze your code projects with AI\n              </p>\n            </div>\n            <Button\n              onClick={() => setIsCreateDialogOpen(true)}\n              className=\"bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200\"\n            >\n              <Plus className=\"w-4 h-4 mr-2\" />\n              New Project\n            </Button>\n          </div>\n\n          {/* Search */}\n          <div className=\"mb-8\">\n            <div className=\"relative max-w-md\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4\" />\n              <Input\n                placeholder=\"Search projects...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50\"\n              />\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Projects Grid */}\n      <div className=\"container mx-auto px-4 pb-8\">\n        {isLoading ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {[...Array(6)].map((_, i) => (\n              <Card key={i} className=\"animate-pulse bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50\">\n                <CardHeader>\n                  <div className=\"h-6 bg-slate-200 dark:bg-slate-700 rounded w-3/4\"></div>\n                  <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-full\"></div>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-2\">\n                    <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2\"></div>\n                    <div className=\"h-4 bg-slate-200 dark:bg-slate-700 rounded w-2/3\"></div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        ) : filteredProjects.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <div className=\"w-20 h-20 bg-slate-100 dark:bg-slate-800 rounded-2xl flex items-center justify-center mx-auto mb-6\">\n              <Code className=\"w-10 h-10 text-slate-400\" />\n            </div>\n            <h3 className=\"text-xl font-semibold mb-2\">\n              {projects.length === 0 ? 'No projects yet' : 'No projects found'}\n            </h3>\n            <p className=\"text-slate-600 dark:text-slate-400 mb-6 max-w-md mx-auto\">\n              {projects.length === 0\n                ? 'Create your first project to start indexing your code and chatting with AI'\n                : 'Try adjusting your search query to find your projects'\n              }\n            </p>\n            {projects.length === 0 && (\n              <Button\n                onClick={() => setIsCreateDialogOpen(true)}\n                className=\"bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200\"\n              >\n                <Plus className=\"w-4 h-4 mr-2\" />\n                Create Your First Project\n              </Button>\n            )}\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredProjects.map((project) => (\n              <Card\n                key={project.id}\n                className=\"group hover:shadow-xl transition-all duration-300 cursor-pointer bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 hover:border-blue-200 dark:hover:border-blue-800 hover:-translate-y-1\"\n                onClick={() => window.location.href = `/projects/${project.id}`}\n              >\n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <CardTitle className=\"text-lg group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors\">\n                        {project.name}\n                      </CardTitle>\n                      {project.description && (\n                        <CardDescription className=\"mt-1 text-slate-600 dark:text-slate-400\">\n                          {project.description}\n                        </CardDescription>\n                      )}\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <div className={`w-2 h-2 rounded-full ${getStatusColor(project.indexingStatus)}`} />\n                      <span className=\"text-xs text-slate-500 dark:text-slate-400\">\n                        {getStatusText(project.indexingStatus)}\n                      </span>\n                    </div>\n                  </div>\n                </CardHeader>\n                <CardContent className=\"pt-0\">\n                  <div className=\"space-y-4\">\n                    {/* Language and Framework */}\n                    <div className=\"flex gap-2 flex-wrap\">\n                      {project.language && (\n                        <Badge variant=\"secondary\" className=\"bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300\">\n                          {project.language}\n                        </Badge>\n                      )}\n                      {project.framework && (\n                        <Badge variant=\"outline\" className=\"border-slate-300 dark:border-slate-600\">\n                          {project.framework}\n                        </Badge>\n                      )}\n                    </div>\n\n                    {/* Stats */}\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div className=\"flex items-center gap-2 text-slate-600 dark:text-slate-400\">\n                        <FileText className=\"w-4 h-4\" />\n                        <span>{project.indexedFiles}/{project.totalFiles} files</span>\n                      </div>\n                      <div className=\"flex items-center gap-2 text-slate-600 dark:text-slate-400\">\n                        <Code className=\"w-4 h-4\" />\n                        <span>{project.totalLines.toLocaleString()} lines</span>\n                      </div>\n                    </div>\n\n                    {/* Last indexed */}\n                    {project.lastIndexedAt && (\n                      <div className=\"flex items-center gap-2 text-xs text-slate-500 dark:text-slate-400\">\n                        <Clock className=\"w-3 h-3\" />\n                        <span>\n                          Indexed {new Date(project.lastIndexedAt).toLocaleDateString()}\n                        </span>\n                      </div>\n                    )}\n\n                    {/* Actions */}\n                    <div className=\"flex gap-2 pt-2\">\n                      <Button\n                        size=\"sm\"\n                        className=\"flex-1 bg-blue-600 hover:bg-blue-700 shadow-md hover:shadow-lg transition-all duration-200\"\n                        onClick={(e) => {\n                          e.stopPropagation()\n                          window.location.href = `/projects/${project.id}`\n                        }}\n                      >\n                        <Zap className=\"w-4 h-4 mr-2\" />\n                        Chat\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        className=\"border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                        onClick={(e) => e.stopPropagation()}\n                      >\n                        <ArrowRight className=\"w-4 h-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Create Project Dialog */}\n      <CreateProjectDialog\n        open={isCreateDialogOpen}\n        onOpenChange={setIsCreateDialogOpen}\n        onProjectCreated={fetchProjects}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AA2BO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ,IAAI,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACvC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,QAAQ,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW,OACnE,QAAQ,QAAQ,EAAE,cAAc,SAAS,YAAY,WAAW;IAGlE,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,SAAM;;;;;YAGN,SAAS,MAAM,KAAK,KAAK,CAAC,0BACzB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAIlC,8OAAC;4CAAG,WAAU;;gDAAsL;8DAElM,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAA6E;;;;;;;;;;;;sDAK/F,8OAAC;4CAAE,WAAU;sDAAoF;;;;;;sDAKjG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,sBAAsB;;sEAErC,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8CAOzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;sDAKpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;sDAKpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,8OAAC;oDAAE,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;8CAOtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAOhB,oBAAoB,iBACpB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAsI;;;;;;kDAGpJ,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAIzD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,sBAAsB;gCACrC,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMrC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;gBAAI,WAAU;0BACZ,0BACC,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,gIAAA,CAAA,OAAI;4BAAS,WAAU;;8CACtB,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;2BARV;;;;;;;;;2BAcb,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,8OAAC;4BAAG,WAAU;sCACX,SAAS,MAAM,KAAK,IAAI,oBAAoB;;;;;;sCAE/C,8OAAC;4BAAE,WAAU;sCACV,SAAS,MAAM,KAAK,IACjB,+EACA;;;;;;wBAGL,SAAS,MAAM,KAAK,mBACnB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,sBAAsB;4BACrC,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;yCAMvC,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,gIAAA,CAAA,OAAI;4BAEH,WAAU;4BACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;;8CAE/D,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,QAAQ,IAAI;;;;;;oDAEd,QAAQ,WAAW,kBAClB,8OAAC,gIAAA,CAAA,kBAAe;wDAAC,WAAU;kEACxB,QAAQ,WAAW;;;;;;;;;;;;0DAI1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,QAAQ,cAAc,GAAG;;;;;;kEAChF,8OAAC;wDAAK,WAAU;kEACb,cAAc,QAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,QAAQ,kBACf,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,QAAQ,QAAQ;;;;;;oDAGpB,QAAQ,SAAS,kBAChB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,QAAQ,SAAS;;;;;;;;;;;;0DAMxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;;oEAAM,QAAQ,YAAY;oEAAC;oEAAE,QAAQ,UAAU;oEAAC;;;;;;;;;;;;;kEAEnD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;;oEAAM,QAAQ,UAAU,CAAC,cAAc;oEAAG;;;;;;;;;;;;;;;;;;;4CAK9C,QAAQ,aAAa,kBACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;;4DAAK;4DACK,IAAI,KAAK,QAAQ,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;0DAMjE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wDAClD;;0EAEA,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGlC,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;wDACV,SAAS,CAAC,IAAM,EAAE,eAAe;kEAEjC,cAAA,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAjFzB,QAAQ,EAAE;;;;;;;;;;;;;;;0BA6FzB,8OAAC,6JAAA,CAAA,sBAAmB;gBAClB,MAAM;gBACN,cAAc;gBACd,kBAAkB;;;;;;;;;;;;AAI1B", "debugId": null}}]}