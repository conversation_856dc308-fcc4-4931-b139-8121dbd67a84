{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/vector.ts"], "sourcesContent": ["import { Index } from \"@upstash/vector\";\n\nlet vectorIndex: Index | null = null;\n\nif (\n  process.env.UPSTASH_VECTOR_REST_URL &&\n  process.env.UPSTASH_VECTOR_REST_TOKEN\n) {\n  vectorIndex = new Index({\n    url: process.env.UPSTASH_VECTOR_REST_URL,\n    token: process.env.UPSTASH_VECTOR_REST_TOKEN,\n  });\n}\n\nexport { vectorIndex };\n\nexport interface CodeMetadata {\n  projectId: string;\n  fileId: string;\n  filePath: string;\n  fileName: string;\n  language?: string;\n  startLine: number;\n  endLine: number;\n  chunkType:\n    | \"function\"\n    | \"class\"\n    | \"interface\"\n    | \"variable\"\n    | \"comment\"\n    | \"general\";\n  symbols?: string[]; // Function names, class names, etc.\n}\n\nexport async function upsertCodeChunk(\n  id: string,\n  content: string,\n  metadata: CodeMetadata\n) {\n  if (!vectorIndex) {\n    console.warn(\"Vector index not configured, skipping upsert\");\n    return { success: false, error: \"Vector index not configured\" };\n  }\n\n  try {\n    await vectorIndex.upsert({\n      id,\n      data: content, // Let Up<PERSON>sh handle embedding generation\n      metadata,\n    });\n    return { success: true };\n  } catch (error) {\n    console.error(\"Error upserting code chunk:\", error);\n    return { success: false, error };\n  }\n}\n\nexport async function searchCode(\n  query: string,\n  projectId?: string,\n  topK: number = 10\n) {\n  if (!vectorIndex) {\n    console.warn(\"Vector index not configured, skipping search\");\n    return {\n      success: false,\n      error: \"Vector index not configured\",\n      results: [],\n    };\n  }\n\n  try {\n    const filter = projectId ? `projectId = '${projectId}'` : undefined;\n\n    const results = await vectorIndex.query({\n      data: query,\n      topK,\n      includeMetadata: true,\n      filter,\n    });\n\n    return {\n      success: true,\n      results: results.map((result) => ({\n        id: result.id,\n        score: result.score,\n        content: result.metadata?.content || \"\",\n        metadata: result.metadata as CodeMetadata,\n      })),\n    };\n  } catch (error) {\n    console.error(\"Error searching code:\", error);\n    return { success: false, error, results: [] };\n  }\n}\n\nexport async function deleteCodeChunks(ids: string[]) {\n  if (!vectorIndex) {\n    console.warn(\"Vector index not configured, skipping delete\");\n    return { success: false, error: \"Vector index not configured\" };\n  }\n\n  try {\n    await vectorIndex.delete(ids);\n    return { success: true };\n  } catch (error) {\n    console.error(\"Error deleting code chunks:\", error);\n    return { success: false, error };\n  }\n}\n\nexport async function getIndexStats() {\n  if (!vectorIndex) {\n    console.warn(\"Vector index not configured, skipping stats\");\n    return { success: false, error: \"Vector index not configured\" };\n  }\n\n  try {\n    const stats = await vectorIndex.info();\n    return { success: true, stats };\n  } catch (error) {\n    console.error(\"Error getting index stats:\", error);\n    return { success: false, error };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAEA,IAAI,cAA4B;AAEhC,IACE,QAAQ,GAAG,CAAC,uBAAuB,IACnC,QAAQ,GAAG,CAAC,yBAAyB,EACrC;IACA,cAAc,IAAI,wKAAA,CAAA,QAAK,CAAC;QACtB,KAAK,QAAQ,GAAG,CAAC,uBAAuB;QACxC,OAAO,QAAQ,GAAG,CAAC,yBAAyB;IAC9C;AACF;;AAsBO,eAAe,gBACpB,EAAU,EACV,OAAe,EACf,QAAsB;IAEtB,IAAI,CAAC,aAAa;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;QAA8B;IAChE;IAEA,IAAI;QACF,MAAM,YAAY,MAAM,CAAC;YACvB;YACA,MAAM;YACN;QACF;QACA,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,eAAe,WACpB,KAAa,EACb,SAAkB,EAClB,OAAe,EAAE;IAEjB,IAAI,CAAC,aAAa;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,SAAS;YACT,OAAO;YACP,SAAS,EAAE;QACb;IACF;IAEA,IAAI;QACF,MAAM,SAAS,YAAY,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,GAAG;QAE1D,MAAM,UAAU,MAAM,YAAY,KAAK,CAAC;YACtC,MAAM;YACN;YACA,iBAAiB;YACjB;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;oBAChC,IAAI,OAAO,EAAE;oBACb,OAAO,OAAO,KAAK;oBACnB,SAAS,OAAO,QAAQ,EAAE,WAAW;oBACrC,UAAU,OAAO,QAAQ;gBAC3B,CAAC;QACH;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;YAAE,SAAS;YAAO;YAAO,SAAS,EAAE;QAAC;IAC9C;AACF;AAEO,eAAe,iBAAiB,GAAa;IAClD,IAAI,CAAC,aAAa;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;QAA8B;IAChE;IAEA,IAAI;QACF,MAAM,YAAY,MAAM,CAAC;QACzB,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,eAAe;IACpB,IAAI,CAAC,aAAa;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;QAA8B;IAChE;IAEA,IAAI;QACF,MAAM,QAAQ,MAAM,YAAY,IAAI;QACpC,OAAO;YAAE,SAAS;YAAM;QAAM;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport * as path from \"path\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// File extension utilities\nexport function getFileExtension(filePath: string): string {\n  return path.extname(filePath).toLowerCase();\n}\n\nexport function getLanguageFromExtension(extension: string): string {\n  const languageMap: Record<string, string> = {\n    \".js\": \"javascript\",\n    \".jsx\": \"javascript\",\n    \".ts\": \"typescript\",\n    \".tsx\": \"typescript\",\n    \".py\": \"python\",\n    \".java\": \"java\",\n    \".cpp\": \"cpp\",\n    \".c\": \"c\",\n    \".cs\": \"csharp\",\n    \".php\": \"php\",\n    \".rb\": \"ruby\",\n    \".go\": \"go\",\n    \".rs\": \"rust\",\n    \".swift\": \"swift\",\n    \".kt\": \"kotlin\",\n    \".scala\": \"scala\",\n    \".sh\": \"bash\",\n    \".sql\": \"sql\",\n    \".html\": \"html\",\n    \".css\": \"css\",\n    \".scss\": \"scss\",\n    \".sass\": \"sass\",\n    \".less\": \"less\",\n    \".vue\": \"vue\",\n    \".svelte\": \"svelte\",\n    \".md\": \"markdown\",\n    \".json\": \"json\",\n    \".xml\": \"xml\",\n    \".yaml\": \"yaml\",\n    \".yml\": \"yaml\",\n    \".toml\": \"toml\",\n    \".ini\": \"ini\",\n    \".cfg\": \"ini\",\n    \".conf\": \"ini\",\n  };\n\n  return languageMap[extension] || \"text\";\n}\n\n// File filtering\nexport function shouldIndexFile(filePath: string): boolean {\n  const extension = getFileExtension(filePath);\n  const fileName = path.basename(filePath);\n\n  // Skip common non-code files\n  const skipExtensions = [\n    \".png\",\n    \".jpg\",\n    \".jpeg\",\n    \".gif\",\n    \".svg\",\n    \".ico\",\n    \".pdf\",\n    \".doc\",\n    \".docx\",\n    \".xls\",\n    \".xlsx\",\n    \".zip\",\n    \".tar\",\n    \".gz\",\n    \".rar\",\n    \".mp3\",\n    \".mp4\",\n    \".avi\",\n    \".mov\",\n    \".exe\",\n    \".dll\",\n    \".so\",\n    \".dylib\",\n    \".log\",\n    \".tmp\",\n    \".cache\",\n    \".lock\",\n    \".pid\",\n  ];\n\n  // Skip common directories and files\n  const skipPatterns = [\n    \"node_modules\",\n    \".git\",\n    \".next\",\n    \".nuxt\",\n    \"dist\",\n    \"build\",\n    \"coverage\",\n    \".nyc_output\",\n    \"vendor\",\n    \"__pycache__\",\n    \".pytest_cache\",\n    \".vscode\",\n    \".idea\",\n    \"package-lock.json\",\n    \"yarn.lock\",\n    \"pnpm-lock.yaml\",\n    \".env\",\n    \".env.local\",\n    \".env.production\",\n    \".DS_Store\",\n    \"Thumbs.db\",\n  ];\n\n  // Check if file should be skipped\n  if (skipExtensions.includes(extension)) {\n    return false;\n  }\n\n  // Check if path contains skip patterns\n  for (const pattern of skipPatterns) {\n    if (filePath.includes(pattern)) {\n      return false;\n    }\n  }\n\n  // Only index files with known extensions or common code files\n  const codeExtensions = [\n    \".js\",\n    \".jsx\",\n    \".ts\",\n    \".tsx\",\n    \".vue\",\n    \".svelte\",\n    \".py\",\n    \".java\",\n    \".cpp\",\n    \".c\",\n    \".cs\",\n    \".php\",\n    \".rb\",\n    \".go\",\n    \".rs\",\n    \".swift\",\n    \".kt\",\n    \".scala\",\n    \".sh\",\n    \".sql\",\n    \".html\",\n    \".css\",\n    \".scss\",\n    \".sass\",\n    \".less\",\n    \".md\",\n    \".json\",\n    \".xml\",\n    \".yaml\",\n    \".yml\",\n    \".toml\",\n    \".ini\",\n    \".cfg\",\n    \".conf\",\n    \".txt\",\n    \".readme\",\n  ];\n\n  return (\n    codeExtensions.includes(extension) ||\n    fileName.toLowerCase().includes(\"readme\")\n  );\n}\n\n// Code chunking\nexport function chunkCode(\n  content: string,\n  maxChunkSize: number = 1000\n): string[] {\n  const lines = content.split(\"\\n\");\n  const chunks: string[] = [];\n  let currentChunk = \"\";\n\n  for (const line of lines) {\n    if (\n      currentChunk.length + line.length + 1 > maxChunkSize &&\n      currentChunk.length > 0\n    ) {\n      chunks.push(currentChunk.trim());\n      currentChunk = line;\n    } else {\n      currentChunk += (currentChunk ? \"\\n\" : \"\") + line;\n    }\n  }\n\n  if (currentChunk.trim()) {\n    chunks.push(currentChunk.trim());\n  }\n\n  return chunks.filter((chunk) => chunk.length > 0);\n}\n\n// Code symbol extraction\nexport function extractCodeSymbols(\n  content: string,\n  language: string\n): string[] {\n  const symbols: string[] = [];\n\n  try {\n    switch (language) {\n      case \"javascript\":\n      case \"typescript\":\n        // Extract function names, class names, etc.\n        const jsPatterns = [\n          /function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n          /class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n          /const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /let\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /var\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:\\s*function/g,\n          /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=>\\s*/g,\n        ];\n\n        for (const pattern of jsPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n\n      case \"python\":\n        // Extract Python function and class names\n        const pyPatterns = [\n          /def\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n        ];\n\n        for (const pattern of pyPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n\n      case \"java\":\n        // Extract Java method and class names\n        const javaPatterns = [\n          /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /interface\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /public\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n          /private\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n          /protected\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n        ];\n\n        for (const pattern of javaPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n    }\n  } catch (error) {\n    console.error(\"Error extracting symbols:\", error);\n  }\n\n  return [...new Set(symbols)]; // Remove duplicates\n}\n\n// File size formatting\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return \"0 Bytes\";\n\n  const k = 1024;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE,UAAU,WAAW;AAC3C;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,cAAsC;QAC1C,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW;QACX,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IAEA,OAAO,WAAW,CAAC,UAAU,IAAI;AACnC;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,MAAM,YAAY,iBAAiB;IACnC,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,WAAa,AAAD,EAAE;IAE/B,6BAA6B;IAC7B,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,oCAAoC;IACpC,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,kCAAkC;IAClC,IAAI,eAAe,QAAQ,CAAC,YAAY;QACtC,OAAO;IACT;IAEA,uCAAuC;IACvC,KAAK,MAAM,WAAW,aAAc;QAClC,IAAI,SAAS,QAAQ,CAAC,UAAU;YAC9B,OAAO;QACT;IACF;IAEA,8DAA8D;IAC9D,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OACE,eAAe,QAAQ,CAAC,cACxB,SAAS,WAAW,GAAG,QAAQ,CAAC;AAEpC;AAGO,SAAS,UACd,OAAe,EACf,eAAuB,IAAI;IAE3B,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,SAAmB,EAAE;IAC3B,IAAI,eAAe;IAEnB,KAAK,MAAM,QAAQ,MAAO;QACxB,IACE,aAAa,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,gBACxC,aAAa,MAAM,GAAG,GACtB;YACA,OAAO,IAAI,CAAC,aAAa,IAAI;YAC7B,eAAe;QACjB,OAAO;YACL,gBAAgB,CAAC,eAAe,OAAO,EAAE,IAAI;QAC/C;IACF;IAEA,IAAI,aAAa,IAAI,IAAI;QACvB,OAAO,IAAI,CAAC,aAAa,IAAI;IAC/B;IAEA,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,GAAG;AACjD;AAGO,SAAS,mBACd,OAAe,EACf,QAAgB;IAEhB,MAAM,UAAoB,EAAE;IAE5B,IAAI;QACF,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,4CAA4C;gBAC5C,MAAM,aAAa;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,WAAY;oBAChC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;YAEF,KAAK;gBACH,0CAA0C;gBAC1C,MAAM,aAAa;oBACjB;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,WAAY;oBAChC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;YAEF,KAAK;gBACH,sCAAsC;gBACtC,MAAM,eAAe;oBACnB;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,aAAc;oBAClC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;QACJ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,OAAO;WAAI,IAAI,IAAI;KAAS,EAAE,oBAAoB;AACpD;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/indexing.ts"], "sourcesContent": ["import * as fs from \"fs\";\nimport * as path from \"path\";\nimport { db } from \"./db\";\nimport { upsertCodeChunk, CodeMetadata } from \"./vector\";\nimport {\n  shouldIndexFile,\n  getFileExtension,\n  getLanguageFromExtension,\n  chunkCode,\n  extractCodeSymbols,\n  formatFileSize,\n} from \"./utils\";\nimport crypto from \"crypto\";\n\nexport interface IndexingProgress {\n  totalFiles: number;\n  processedFiles: number;\n  currentFile: string;\n  status: \"scanning\" | \"indexing\" | \"completed\" | \"failed\";\n  error?: string;\n}\n\nexport async function indexProject(\n  projectId: string,\n  onProgress?: (progress: IndexingProgress) => void,\n  incrementalOnly: boolean = false\n) {\n  try {\n    const project = await db.project.findUnique({\n      where: { id: projectId },\n    });\n\n    if (!project) {\n      throw new Error(\"Project not found\");\n    }\n\n    // Update project status\n    await db.project.update({\n      where: { id: projectId },\n      data: { indexingStatus: \"indexing\" },\n    });\n\n    const progressUpdate = {\n      totalFiles: 0,\n      processedFiles: 0,\n      currentFile: \"Scanning files...\",\n      status: \"scanning\" as const,\n    };\n\n    onProgress?.(progressUpdate);\n\n    // Update progress in global store for real-time tracking\n    if (typeof globalThis !== \"undefined\") {\n      globalThis.indexingProgress = globalThis.indexingProgress || new Map();\n      globalThis.indexingProgress.set(projectId, {\n        ...progressUpdate,\n        startTime: new Date(),\n      });\n    }\n\n    let filesToProcess: Array<{\n      path: string;\n      relativePath: string;\n      name: string;\n      size: number;\n      lines: number;\n    }> = [];\n\n    if (incrementalOnly) {\n      // Only process files that are not indexed or have been updated\n      const unindexedFiles = await db.projectFile.findMany({\n        where: {\n          projectId,\n          isIndexed: false,\n        },\n      });\n\n      // Convert database files to the expected format\n      filesToProcess = unindexedFiles.map((file) => ({\n        path: `${project.path}/${file.path}`,\n        relativePath: file.path,\n        name: file.name,\n        size: file.size,\n        lines: file.lines,\n      }));\n    } else {\n      // Scan directory for all files\n      const files = await scanDirectory(project.path);\n      filesToProcess = files.filter((file) => shouldIndexFile(file.path));\n    }\n\n    const indexableFiles = filesToProcess;\n\n    const indexingUpdate = {\n      totalFiles: indexableFiles.length,\n      processedFiles: 0,\n      currentFile: \"Starting indexing...\",\n      status: \"indexing\" as const,\n    };\n\n    onProgress?.(indexingUpdate);\n\n    // Update global progress\n    if (typeof globalThis !== \"undefined\" && globalThis.indexingProgress) {\n      const existing = globalThis.indexingProgress.get(projectId);\n      globalThis.indexingProgress.set(projectId, {\n        ...existing,\n        ...indexingUpdate,\n      });\n    }\n\n    let processedCount = 0;\n    let totalLines = 0;\n\n    for (const file of indexableFiles) {\n      try {\n        const fileUpdate = {\n          totalFiles: indexableFiles.length,\n          processedFiles: processedCount,\n          currentFile: file.relativePath,\n          status: \"indexing\" as const,\n        };\n\n        onProgress?.(fileUpdate);\n\n        // Update global progress\n        if (typeof globalThis !== \"undefined\" && globalThis.indexingProgress) {\n          const existing = globalThis.indexingProgress.get(projectId);\n          globalThis.indexingProgress.set(projectId, {\n            ...existing,\n            ...fileUpdate,\n          });\n        }\n\n        await indexFile(projectId, file);\n        totalLines += file.lines;\n        processedCount++;\n      } catch (error) {\n        console.error(`Error indexing file ${file.path}:`, error);\n        // Continue with other files\n      }\n    }\n\n    // Update project statistics\n    await db.project.update({\n      where: { id: projectId },\n      data: {\n        indexingStatus: \"completed\",\n        isIndexed: true,\n        totalFiles: indexableFiles.length,\n        indexedFiles: processedCount,\n        totalLines,\n        lastIndexedAt: new Date(),\n      },\n    });\n\n    const completedUpdate = {\n      totalFiles: indexableFiles.length,\n      processedFiles: processedCount,\n      currentFile: \"Completed!\",\n      status: \"completed\" as const,\n    };\n\n    onProgress?.(completedUpdate);\n\n    // Update global progress\n    if (typeof globalThis !== \"undefined\" && globalThis.indexingProgress) {\n      const existing = globalThis.indexingProgress.get(projectId);\n      globalThis.indexingProgress.set(projectId, {\n        ...existing,\n        ...completedUpdate,\n      });\n\n      // Clear progress after 30 seconds\n      setTimeout(() => {\n        if (globalThis.indexingProgress) {\n          globalThis.indexingProgress.delete(projectId);\n        }\n      }, 30000);\n    }\n\n    return { success: true, processedFiles: processedCount, totalLines };\n  } catch (error) {\n    console.error(\"Error indexing project:\", error);\n\n    await db.project.update({\n      where: { id: projectId },\n      data: { indexingStatus: \"failed\" },\n    });\n\n    const errorUpdate = {\n      totalFiles: 0,\n      processedFiles: 0,\n      currentFile: \"\",\n      status: \"failed\" as const,\n      error: error instanceof Error ? error.message : \"Unknown error\",\n    };\n\n    onProgress?.(errorUpdate);\n\n    // Update global progress\n    if (typeof globalThis !== \"undefined\" && globalThis.indexingProgress) {\n      const existing = globalThis.indexingProgress.get(projectId);\n      globalThis.indexingProgress.set(projectId, {\n        ...existing,\n        ...errorUpdate,\n      });\n    }\n\n    return { success: false, error };\n  }\n}\n\nasync function scanDirectory(dirPath: string): Promise<\n  Array<{\n    path: string;\n    relativePath: string;\n    name: string;\n    size: number;\n    lines: number;\n  }>\n> {\n  const files: Array<{\n    path: string;\n    relativePath: string;\n    name: string;\n    size: number;\n    lines: number;\n  }> = [];\n\n  async function scanRecursive(currentPath: string, relativePath: string = \"\") {\n    try {\n      const entries = await fs.promises.readdir(currentPath, {\n        withFileTypes: true,\n      });\n\n      for (const entry of entries) {\n        const fullPath = path.join(currentPath, entry.name);\n        const relPath = path.join(relativePath, entry.name);\n\n        if (entry.isDirectory()) {\n          // Skip common directories that shouldn't be indexed\n          const skipDirs = [\n            \"node_modules\",\n            \".git\",\n            \"dist\",\n            \"build\",\n            \".next\",\n            \"coverage\",\n            \"vendor\",\n            \"__pycache__\",\n            \"target\",\n            \"bin\",\n            \"obj\",\n          ];\n          if (!skipDirs.includes(entry.name)) {\n            await scanRecursive(fullPath, relPath);\n          }\n        } else if (entry.isFile()) {\n          try {\n            const stats = await fs.promises.stat(fullPath);\n            const content = await fs.promises.readFile(fullPath, \"utf-8\");\n            const lines = content.split(\"\\n\").length;\n\n            files.push({\n              path: fullPath,\n              relativePath: relPath,\n              name: entry.name,\n              size: stats.size,\n              lines,\n            });\n          } catch (error) {\n            // Skip files that can't be read\n            console.warn(`Skipping file ${fullPath}:`, error);\n          }\n        }\n      }\n    } catch (error) {\n      console.warn(`Error scanning directory ${currentPath}:`, error);\n    }\n  }\n\n  await scanRecursive(dirPath);\n  return files;\n}\n\nasync function indexFile(\n  projectId: string,\n  file: {\n    path: string;\n    relativePath: string;\n    name: string;\n    size: number;\n    lines: number;\n  }\n) {\n  const content = await fs.promises.readFile(file.path, \"utf-8\");\n  const hash = crypto.createHash(\"md5\").update(content).digest(\"hex\");\n  const extension = getFileExtension(file.name);\n  const language = getLanguageFromExtension(extension);\n\n  // Check if file already exists and hasn't changed\n  const existingFile = await db.projectFile.findFirst({\n    where: {\n      projectId,\n      path: file.relativePath,\n    },\n  });\n\n  if (existingFile && existingFile.hash === hash && existingFile.isIndexed) {\n    // File hasn't changed, skip indexing\n    return;\n  }\n\n  // Create or update file record\n  const projectFile = await db.projectFile.upsert({\n    where: {\n      projectId_path: {\n        projectId,\n        path: file.relativePath,\n      },\n    },\n    update: {\n      name: file.name,\n      extension,\n      size: file.size,\n      lines: file.lines,\n      content: file.size < 100000 ? content : null, // Store content for smaller files\n      hash,\n      isIndexed: false,\n    },\n    create: {\n      projectId,\n      path: file.relativePath,\n      name: file.name,\n      extension,\n      size: file.size,\n      lines: file.lines,\n      content: file.size < 100000 ? content : null,\n      hash,\n      isIndexed: false,\n    },\n  });\n\n  // Delete existing chunks\n  await db.fileChunk.deleteMany({\n    where: { fileId: projectFile.id },\n  });\n\n  // Chunk the file content\n  const chunks = chunkCode(content, 1000);\n  const symbols = extractCodeSymbols(content, language);\n\n  // Index each chunk\n  for (let i = 0; i < chunks.length; i++) {\n    const chunk = chunks[i];\n    const chunkId = `${projectFile.id}-chunk-${i}`;\n\n    const metadata: CodeMetadata = {\n      projectId,\n      fileId: projectFile.id,\n      filePath: file.relativePath,\n      fileName: file.name,\n      language,\n      startLine: chunk.startLine,\n      endLine: chunk.endLine,\n      chunkType: \"general\",\n      symbols: symbols.length > 0 ? symbols : undefined,\n    };\n\n    // Upsert to vector database\n    const result = await upsertCodeChunk(chunkId, chunk.content, metadata);\n\n    if (result.success) {\n      // Save chunk to database\n      await db.fileChunk.create({\n        data: {\n          fileId: projectFile.id,\n          content: chunk.content,\n          startLine: chunk.startLine,\n          endLine: chunk.endLine,\n          vectorId: chunkId,\n        },\n      });\n    }\n  }\n\n  // Mark file as indexed\n  await db.projectFile.update({\n    where: { id: projectFile.id },\n    data: {\n      isIndexed: true,\n      lastIndexedAt: new Date(),\n    },\n  });\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAQA;;;;;;;AAUO,eAAe,aACpB,SAAiB,EACjB,UAAiD,EACjD,kBAA2B,KAAK;IAEhC,IAAI;QACF,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE,IAAI;YAAU;QACzB;QAEA,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,wBAAwB;QACxB,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;YACtB,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBAAE,gBAAgB;YAAW;QACrC;QAEA,MAAM,iBAAiB;YACrB,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,QAAQ;QACV;QAEA,aAAa;QAEb,yDAAyD;QACzD,IAAI,OAAO,eAAe,aAAa;YACrC,WAAW,gBAAgB,GAAG,WAAW,gBAAgB,IAAI,IAAI;YACjE,WAAW,gBAAgB,CAAC,GAAG,CAAC,WAAW;gBACzC,GAAG,cAAc;gBACjB,WAAW,IAAI;YACjB;QACF;QAEA,IAAI,iBAMC,EAAE;QAEP,IAAI,iBAAiB;YACnB,+DAA+D;YAC/D,MAAM,iBAAiB,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,QAAQ,CAAC;gBACnD,OAAO;oBACL;oBACA,WAAW;gBACb;YACF;YAEA,gDAAgD;YAChD,iBAAiB,eAAe,GAAG,CAAC,CAAC,OAAS,CAAC;oBAC7C,MAAM,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;oBACpC,cAAc,KAAK,IAAI;oBACvB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;gBACnB,CAAC;QACH,OAAO;YACL,+BAA+B;YAC/B,MAAM,QAAQ,MAAM,cAAc,QAAQ,IAAI;YAC9C,iBAAiB,MAAM,MAAM,CAAC,CAAC,OAAS,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,IAAI;QACnE;QAEA,MAAM,iBAAiB;QAEvB,MAAM,iBAAiB;YACrB,YAAY,eAAe,MAAM;YACjC,gBAAgB;YAChB,aAAa;YACb,QAAQ;QACV;QAEA,aAAa;QAEb,yBAAyB;QACzB,IAAI,OAAO,eAAe,eAAe,WAAW,gBAAgB,EAAE;YACpE,MAAM,WAAW,WAAW,gBAAgB,CAAC,GAAG,CAAC;YACjD,WAAW,gBAAgB,CAAC,GAAG,CAAC,WAAW;gBACzC,GAAG,QAAQ;gBACX,GAAG,cAAc;YACnB;QACF;QAEA,IAAI,iBAAiB;QACrB,IAAI,aAAa;QAEjB,KAAK,MAAM,QAAQ,eAAgB;YACjC,IAAI;gBACF,MAAM,aAAa;oBACjB,YAAY,eAAe,MAAM;oBACjC,gBAAgB;oBAChB,aAAa,KAAK,YAAY;oBAC9B,QAAQ;gBACV;gBAEA,aAAa;gBAEb,yBAAyB;gBACzB,IAAI,OAAO,eAAe,eAAe,WAAW,gBAAgB,EAAE;oBACpE,MAAM,WAAW,WAAW,gBAAgB,CAAC,GAAG,CAAC;oBACjD,WAAW,gBAAgB,CAAC,GAAG,CAAC,WAAW;wBACzC,GAAG,QAAQ;wBACX,GAAG,UAAU;oBACf;gBACF;gBAEA,MAAM,UAAU,WAAW;gBAC3B,cAAc,KAAK,KAAK;gBACxB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YACnD,4BAA4B;YAC9B;QACF;QAEA,4BAA4B;QAC5B,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;YACtB,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ,gBAAgB;gBAChB,WAAW;gBACX,YAAY,eAAe,MAAM;gBACjC,cAAc;gBACd;gBACA,eAAe,IAAI;YACrB;QACF;QAEA,MAAM,kBAAkB;YACtB,YAAY,eAAe,MAAM;YACjC,gBAAgB;YAChB,aAAa;YACb,QAAQ;QACV;QAEA,aAAa;QAEb,yBAAyB;QACzB,IAAI,OAAO,eAAe,eAAe,WAAW,gBAAgB,EAAE;YACpE,MAAM,WAAW,WAAW,gBAAgB,CAAC,GAAG,CAAC;YACjD,WAAW,gBAAgB,CAAC,GAAG,CAAC,WAAW;gBACzC,GAAG,QAAQ;gBACX,GAAG,eAAe;YACpB;YAEA,kCAAkC;YAClC,WAAW;gBACT,IAAI,WAAW,gBAAgB,EAAE;oBAC/B,WAAW,gBAAgB,CAAC,MAAM,CAAC;gBACrC;YACF,GAAG;QACL;QAEA,OAAO;YAAE,SAAS;YAAM,gBAAgB;YAAgB;QAAW;IACrE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;YACtB,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBAAE,gBAAgB;YAAS;QACnC;QAEA,MAAM,cAAc;YAClB,YAAY;YACZ,gBAAgB;YAChB,aAAa;YACb,QAAQ;YACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;QAEA,aAAa;QAEb,yBAAyB;QACzB,IAAI,OAAO,eAAe,eAAe,WAAW,gBAAgB,EAAE;YACpE,MAAM,WAAW,WAAW,gBAAgB,CAAC,GAAG,CAAC;YACjD,WAAW,gBAAgB,CAAC,GAAG,CAAC,WAAW;gBACzC,GAAG,QAAQ;gBACX,GAAG,WAAW;YAChB;QACF;QAEA,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEA,eAAe,cAAc,OAAe;IAS1C,MAAM,QAMD,EAAE;IAEP,eAAe,cAAc,WAAmB,EAAE,eAAuB,EAAE;QACzE,IAAI;YACF,MAAM,UAAU,MAAM,6FAAA,CAAA,WAAW,CAAC,OAAO,CAAC,aAAa;gBACrD,eAAe;YACjB;YAEA,KAAK,MAAM,SAAS,QAAS;gBAC3B,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAS,AAAD,EAAE,aAAa,MAAM,IAAI;gBAClD,MAAM,UAAU,CAAA,GAAA,iGAAA,CAAA,OAAS,AAAD,EAAE,cAAc,MAAM,IAAI;gBAElD,IAAI,MAAM,WAAW,IAAI;oBACvB,oDAAoD;oBACpD,MAAM,WAAW;wBACf;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM,IAAI,GAAG;wBAClC,MAAM,cAAc,UAAU;oBAChC;gBACF,OAAO,IAAI,MAAM,MAAM,IAAI;oBACzB,IAAI;wBACF,MAAM,QAAQ,MAAM,6FAAA,CAAA,WAAW,CAAC,IAAI,CAAC;wBACrC,MAAM,UAAU,MAAM,6FAAA,CAAA,WAAW,CAAC,QAAQ,CAAC,UAAU;wBACrD,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM;wBAExC,MAAM,IAAI,CAAC;4BACT,MAAM;4BACN,cAAc;4BACd,MAAM,MAAM,IAAI;4BAChB,MAAM,MAAM,IAAI;4BAChB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,gCAAgC;wBAChC,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;oBAC7C;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,yBAAyB,EAAE,YAAY,CAAC,CAAC,EAAE;QAC3D;IACF;IAEA,MAAM,cAAc;IACpB,OAAO;AACT;AAEA,eAAe,UACb,SAAiB,EACjB,IAMC;IAED,MAAM,UAAU,MAAM,6FAAA,CAAA,WAAW,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;IACtD,MAAM,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC;IAC7D,MAAM,YAAY,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI;IAC5C,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,2BAAwB,AAAD,EAAE;IAE1C,kDAAkD;IAClD,MAAM,eAAe,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,SAAS,CAAC;QAClD,OAAO;YACL;YACA,MAAM,KAAK,YAAY;QACzB;IACF;IAEA,IAAI,gBAAgB,aAAa,IAAI,KAAK,QAAQ,aAAa,SAAS,EAAE;QACxE,qCAAqC;QACrC;IACF;IAEA,+BAA+B;IAC/B,MAAM,cAAc,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,MAAM,CAAC;QAC9C,OAAO;YACL,gBAAgB;gBACd;gBACA,MAAM,KAAK,YAAY;YACzB;QACF;QACA,QAAQ;YACN,MAAM,KAAK,IAAI;YACf;YACA,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,IAAI,GAAG,SAAS,UAAU;YACxC;YACA,WAAW;QACb;QACA,QAAQ;YACN;YACA,MAAM,KAAK,YAAY;YACvB,MAAM,KAAK,IAAI;YACf;YACA,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,IAAI,GAAG,SAAS,UAAU;YACxC;YACA,WAAW;QACb;IACF;IAEA,yBAAyB;IACzB,MAAM,kHAAA,CAAA,KAAE,CAAC,SAAS,CAAC,UAAU,CAAC;QAC5B,OAAO;YAAE,QAAQ,YAAY,EAAE;QAAC;IAClC;IAEA,yBAAyB;IACzB,MAAM,SAAS,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,SAAS;IAClC,MAAM,UAAU,CAAA,GAAA,qHAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;IAE5C,mBAAmB;IACnB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,QAAQ,MAAM,CAAC,EAAE;QACvB,MAAM,UAAU,GAAG,YAAY,EAAE,CAAC,OAAO,EAAE,GAAG;QAE9C,MAAM,WAAyB;YAC7B;YACA,QAAQ,YAAY,EAAE;YACtB,UAAU,KAAK,YAAY;YAC3B,UAAU,KAAK,IAAI;YACnB;YACA,WAAW,MAAM,SAAS;YAC1B,SAAS,MAAM,OAAO;YACtB,WAAW;YACX,SAAS,QAAQ,MAAM,GAAG,IAAI,UAAU;QAC1C;QAEA,4BAA4B;QAC5B,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,MAAM,OAAO,EAAE;QAE7D,IAAI,OAAO,OAAO,EAAE;YAClB,yBAAyB;YACzB,MAAM,kHAAA,CAAA,KAAE,CAAC,SAAS,CAAC,MAAM,CAAC;gBACxB,MAAM;oBACJ,QAAQ,YAAY,EAAE;oBACtB,SAAS,MAAM,OAAO;oBACtB,WAAW,MAAM,SAAS;oBAC1B,SAAS,MAAM,OAAO;oBACtB,UAAU;gBACZ;YACF;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,MAAM,CAAC;QAC1B,OAAO;YAAE,IAAI,YAAY,EAAE;QAAC;QAC5B,MAAM;YACJ,WAAW;YACX,eAAe,IAAI;QACrB;IACF;AACF", "debugId": null}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/app/api/projects/%5Bid%5D/index/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { db } from \"@/lib/db\";\nimport { indexProject } from \"@/lib/indexing\";\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params;\n    const project = await db.project.findUnique({\n      where: { id },\n    });\n\n    if (!project) {\n      return NextResponse.json({ error: \"Project not found\" }, { status: 404 });\n    }\n\n    // Check if already indexing\n    if (project.indexingStatus === \"indexing\") {\n      return NextResponse.json(\n        { error: \"Project is already being indexed\" },\n        { status: 400 }\n      );\n    }\n\n    // Parse request body to check for incremental flag\n    const body = await request.json().catch(() => ({}));\n    const incremental = body.incremental === true;\n\n    // Start indexing in background\n    indexProject(id, undefined, incremental).catch((error) => {\n      console.error(\"Background indexing failed:\", error);\n    });\n\n    return NextResponse.json({\n      message: incremental\n        ? \"Incremental indexing started\"\n        : \"Full indexing started\",\n      status: \"indexing\",\n      incremental,\n    });\n  } catch (error) {\n    console.error(\"Error starting indexing:\", error);\n    return NextResponse.json(\n      { error: \"Failed to start indexing\" },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params;\n    const project = await db.project.findUnique({\n      where: { id },\n      include: {\n        files: {\n          select: {\n            id: true,\n            path: true,\n            name: true,\n            extension: true,\n            size: true,\n            lines: true,\n            isIndexed: true,\n            lastIndexedAt: true,\n          },\n          orderBy: {\n            path: \"asc\",\n          },\n        },\n      },\n    });\n\n    if (!project) {\n      return NextResponse.json({ error: \"Project not found\" }, { status: 404 });\n    }\n\n    return NextResponse.json({\n      project: {\n        id: project.id,\n        name: project.name,\n        indexingStatus: project.indexingStatus,\n        totalFiles: project.totalFiles,\n        indexedFiles: project.indexedFiles,\n        totalLines: project.totalLines,\n        lastIndexedAt: project.lastIndexedAt,\n      },\n      files: project.files,\n    });\n  } catch (error) {\n    console.error(\"Error fetching indexing status:\", error);\n    return NextResponse.json(\n      { error: \"Failed to fetch indexing status\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,cAAc,KAAK,YAAY;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmC,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,mDAAmD;QACnD,MAAM,OAAO,MAAM,QAAQ,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACjD,MAAM,cAAc,KAAK,WAAW,KAAK;QAEzC,+BAA+B;QAC/B,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,IAAI,WAAW,aAAa,KAAK,CAAC,CAAC;YAC9C,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,cACL,iCACA;YACJ,QAAQ;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,OAAO;oBACL,QAAQ;wBACN,IAAI;wBACJ,MAAM;wBACN,MAAM;wBACN,WAAW;wBACX,MAAM;wBACN,OAAO;wBACP,WAAW;wBACX,eAAe;oBACjB;oBACA,SAAS;wBACP,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;gBACP,IAAI,QAAQ,EAAE;gBACd,MAAM,QAAQ,IAAI;gBAClB,gBAAgB,QAAQ,cAAc;gBACtC,YAAY,QAAQ,UAAU;gBAC9B,cAAc,QAAQ,YAAY;gBAClC,YAAY,QAAQ,UAAU;gBAC9B,eAAe,QAAQ,aAAa;YACtC;YACA,OAAO,QAAQ,KAAK;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}