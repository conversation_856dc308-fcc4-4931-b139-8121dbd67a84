{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport * as path from \"path\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// File extension utilities\nexport function getFileExtension(filePath: string): string {\n  return path.extname(filePath).toLowerCase();\n}\n\nexport function getLanguageFromExtension(extension: string): string {\n  const languageMap: Record<string, string> = {\n    \".js\": \"javascript\",\n    \".jsx\": \"javascript\",\n    \".ts\": \"typescript\",\n    \".tsx\": \"typescript\",\n    \".py\": \"python\",\n    \".java\": \"java\",\n    \".cpp\": \"cpp\",\n    \".c\": \"c\",\n    \".cs\": \"csharp\",\n    \".php\": \"php\",\n    \".rb\": \"ruby\",\n    \".go\": \"go\",\n    \".rs\": \"rust\",\n    \".swift\": \"swift\",\n    \".kt\": \"kotlin\",\n    \".scala\": \"scala\",\n    \".sh\": \"bash\",\n    \".sql\": \"sql\",\n    \".html\": \"html\",\n    \".css\": \"css\",\n    \".scss\": \"scss\",\n    \".sass\": \"sass\",\n    \".less\": \"less\",\n    \".vue\": \"vue\",\n    \".svelte\": \"svelte\",\n    \".md\": \"markdown\",\n    \".json\": \"json\",\n    \".xml\": \"xml\",\n    \".yaml\": \"yaml\",\n    \".yml\": \"yaml\",\n    \".toml\": \"toml\",\n    \".ini\": \"ini\",\n    \".cfg\": \"ini\",\n    \".conf\": \"ini\",\n  };\n\n  return languageMap[extension] || \"text\";\n}\n\n// File filtering\nconst SKIP_EXTENSIONS = [\n  // Images\n  \".png\",\n  \".jpg\",\n  \".jpeg\",\n  \".gif\",\n  \".svg\",\n  \".ico\",\n  \".webp\",\n  \".bmp\",\n  \".tiff\",\n  // Documents\n  \".pdf\",\n  \".doc\",\n  \".docx\",\n  \".xls\",\n  \".xlsx\",\n  \".ppt\",\n  \".pptx\",\n  // Archives\n  \".zip\",\n  \".tar\",\n  \".gz\",\n  \".rar\",\n  \".7z\",\n  \".bz2\",\n  \".xz\",\n  // Media\n  \".mp3\",\n  \".mp4\",\n  \".avi\",\n  \".mov\",\n  \".wmv\",\n  \".flv\",\n  \".mkv\",\n  \".wav\",\n  \".ogg\",\n  // Executables\n  \".exe\",\n  \".dll\",\n  \".so\",\n  \".dylib\",\n  \".app\",\n  \".deb\",\n  \".rpm\",\n  \".msi\",\n  // Temporary/Cache\n  \".log\",\n  \".tmp\",\n  \".cache\",\n  \".lock\",\n  \".pid\",\n  \".swp\",\n  \".swo\",\n  \".bak\",\n  // Font files\n  \".ttf\",\n  \".otf\",\n  \".woff\",\n  \".woff2\",\n  \".eot\",\n  // Database files\n  \".db\",\n  \".sqlite\",\n  \".sqlite3\",\n];\n\nconst SKIP_PATTERNS = [\n  // Dependencies\n  \"node_modules\",\n  \"vendor\",\n  \"__pycache__\",\n  \".pytest_cache\",\n  // Version control\n  \".git\",\n  \".svn\",\n  \".hg\",\n  // Build outputs\n  \".next\",\n  \".nuxt\",\n  \"dist\",\n  \"build\",\n  \"out\",\n  \"target\",\n  \"bin\",\n  \"obj\",\n  // IDE/Editor\n  \".vscode\",\n  \".idea\",\n  \".vs\",\n  \".sublime-project\",\n  \".sublime-workspace\",\n  // Testing/Coverage\n  \"coverage\",\n  \".nyc_output\",\n  \".coverage\",\n  \"htmlcov\",\n  // Lock files\n  \"package-lock.json\",\n  \"yarn.lock\",\n  \"pnpm-lock.yaml\",\n  \"composer.lock\",\n  \"Pipfile.lock\",\n  // Environment files\n  \".env\",\n  \".env.local\",\n  \".env.development\",\n  \".env.production\",\n  \".env.staging\",\n  // OS files\n  \".DS_Store\",\n  \"Thumbs.db\",\n  \"desktop.ini\",\n  // Logs\n  \"logs\",\n  \"*.log\",\n  // Cache directories\n  \".cache\",\n  \".parcel-cache\",\n  \".webpack\",\n  \".rollup.cache\",\n];\n\nconst SKIP_FILENAMES = [\n  // Lock files (exact matches)\n  \"package-lock.json\",\n  \"yarn.lock\",\n  \"pnpm-lock.yaml\",\n  \"composer.lock\",\n  \"Pipfile.lock\",\n  // Environment files\n  \".env\",\n  \".env.local\",\n  \".env.development\",\n  \".env.production\",\n  \".env.staging\",\n  // OS files\n  \".DS_Store\",\n  \"Thumbs.db\",\n  \"desktop.ini\",\n  // IDE files\n  \".gitignore\",\n  \".gitattributes\",\n  \".editorconfig\",\n];\n\nconst CODE_EXTENSIONS = [\n  // Web technologies\n  \".js\",\n  \".jsx\",\n  \".ts\",\n  \".tsx\",\n  \".vue\",\n  \".svelte\",\n  \".astro\",\n  \".html\",\n  \".htm\",\n  \".css\",\n  \".scss\",\n  \".sass\",\n  \".less\",\n  \".stylus\",\n  // Programming languages\n  \".py\",\n  \".java\",\n  \".cpp\",\n  \".c\",\n  \".cs\",\n  \".php\",\n  \".rb\",\n  \".go\",\n  \".rs\",\n  \".swift\",\n  \".kt\",\n  \".scala\",\n  \".clj\",\n  \".cljs\",\n  \".hs\",\n  \".elm\",\n  \".dart\",\n  \".lua\",\n  \".perl\",\n  \".r\",\n  \".matlab\",\n  \".julia\",\n  \".f90\",\n  \".f95\",\n  // Shell/Scripts\n  \".sh\",\n  \".bash\",\n  \".zsh\",\n  \".fish\",\n  \".ps1\",\n  \".bat\",\n  \".cmd\",\n  // Data/Config\n  \".json\",\n  \".xml\",\n  \".yaml\",\n  \".yml\",\n  \".toml\",\n  \".ini\",\n  \".cfg\",\n  \".conf\",\n  \".properties\",\n  \".env.example\",\n  \".env.template\",\n  // Database\n  \".sql\",\n  \".graphql\",\n  \".gql\",\n  // Documentation\n  \".md\",\n  \".mdx\",\n  \".txt\",\n  \".rst\",\n  \".adoc\",\n  // Other\n  \".dockerfile\",\n  \".gitignore\",\n  \".gitattributes\",\n  \".editorconfig\",\n];\n\nfunction shouldSkipPath(filePath: string): boolean {\n  const normalizedPath = filePath.replace(/\\\\/g, \"/\");\n  const pathParts = normalizedPath.split(\"/\");\n\n  // Check if any part of the path matches skip patterns\n  for (const part of pathParts) {\n    for (const pattern of SKIP_PATTERNS) {\n      if (part === pattern || part.includes(pattern)) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n}\n\nexport function shouldUploadFile(filePath: string): boolean {\n  const extension = getFileExtension(filePath);\n  const fileName = path.basename(filePath);\n\n  // Skip files with unwanted extensions\n  if (SKIP_EXTENSIONS.includes(extension)) {\n    return false;\n  }\n\n  // Skip specific filenames\n  if (SKIP_FILENAMES.includes(fileName)) {\n    return false;\n  }\n\n  // Skip paths containing unwanted patterns\n  if (shouldSkipPath(filePath)) {\n    return false;\n  }\n\n  // Only allow files with code extensions or common project files\n  return (\n    CODE_EXTENSIONS.includes(extension) ||\n    fileName.toLowerCase().includes(\"readme\") ||\n    fileName.toLowerCase().includes(\"license\") ||\n    fileName.toLowerCase().includes(\"changelog\") ||\n    fileName.toLowerCase().includes(\"makefile\") ||\n    fileName === \"Dockerfile\" ||\n    fileName === \"docker-compose.yml\" ||\n    fileName === \"docker-compose.yaml\"\n  );\n}\n\nexport function shouldIndexFile(filePath: string): boolean {\n  // Use the same logic as shouldUploadFile for consistency\n  return shouldUploadFile(filePath);\n}\n\n// Code chunking\nexport function chunkCode(\n  content: string,\n  maxChunkSize: number = 1000\n): string[] {\n  const lines = content.split(\"\\n\");\n  const chunks: string[] = [];\n  let currentChunk = \"\";\n\n  for (const line of lines) {\n    if (\n      currentChunk.length + line.length + 1 > maxChunkSize &&\n      currentChunk.length > 0\n    ) {\n      chunks.push(currentChunk.trim());\n      currentChunk = line;\n    } else {\n      currentChunk += (currentChunk ? \"\\n\" : \"\") + line;\n    }\n  }\n\n  if (currentChunk.trim()) {\n    chunks.push(currentChunk.trim());\n  }\n\n  return chunks.filter((chunk) => chunk.length > 0);\n}\n\n// Code symbol extraction\nexport function extractCodeSymbols(\n  content: string,\n  language: string\n): string[] {\n  const symbols: string[] = [];\n\n  try {\n    switch (language) {\n      case \"javascript\":\n      case \"typescript\":\n        // Extract function names, class names, etc.\n        const jsPatterns = [\n          /function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n          /class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n          /const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /let\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /var\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:\\s*function/g,\n          /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=>\\s*/g,\n        ];\n\n        for (const pattern of jsPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n\n      case \"python\":\n        // Extract Python function and class names\n        const pyPatterns = [\n          /def\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n        ];\n\n        for (const pattern of pyPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n\n      case \"java\":\n        // Extract Java method and class names\n        const javaPatterns = [\n          /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /interface\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /public\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n          /private\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n          /protected\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n        ];\n\n        for (const pattern of javaPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n    }\n  } catch (error) {\n    console.error(\"Error extracting symbols:\", error);\n  }\n\n  return [...new Set(symbols)]; // Remove duplicates\n}\n\n// File size formatting\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return \"0 Bytes\";\n\n  const k = 1024;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,CAAA,GAAA,0KAAA,CAAA,UAAY,AAAD,EAAE,UAAU,WAAW;AAC3C;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,cAAsC;QAC1C,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW;QACX,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IAEA,OAAO,WAAW,CAAC,UAAU,IAAI;AACnC;AAEA,iBAAiB;AACjB,MAAM,kBAAkB;IACtB,SAAS;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,YAAY;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,kBAAkB;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,iBAAiB;IACjB;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IACpB,eAAe;IACf;IACA;IACA;IACA;IACA,kBAAkB;IAClB;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,mBAAmB;IACnB;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,OAAO;IACP;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB,6BAA6B;IAC7B;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,YAAY;IACZ;IACA;IACA;CACD;AAED,MAAM,kBAAkB;IACtB,mBAAmB;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,wBAAwB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;IACA;IACA;IACA;CACD;AAED,SAAS,eAAe,QAAgB;IACtC,MAAM,iBAAiB,SAAS,OAAO,CAAC,OAAO;IAC/C,MAAM,YAAY,eAAe,KAAK,CAAC;IAEvC,sDAAsD;IACtD,KAAK,MAAM,QAAQ,UAAW;QAC5B,KAAK,MAAM,WAAW,cAAe;YACnC,IAAI,SAAS,WAAW,KAAK,QAAQ,CAAC,UAAU;gBAC9C,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,MAAM,YAAY,iBAAiB;IACnC,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,WAAa,AAAD,EAAE;IAE/B,sCAAsC;IACtC,IAAI,gBAAgB,QAAQ,CAAC,YAAY;QACvC,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,eAAe,QAAQ,CAAC,WAAW;QACrC,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,eAAe,WAAW;QAC5B,OAAO;IACT;IAEA,gEAAgE;IAChE,OACE,gBAAgB,QAAQ,CAAC,cACzB,SAAS,WAAW,GAAG,QAAQ,CAAC,aAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,cAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,gBAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,eAChC,aAAa,gBACb,aAAa,wBACb,aAAa;AAEjB;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,yDAAyD;IACzD,OAAO,iBAAiB;AAC1B;AAGO,SAAS,UACd,OAAe,EACf,eAAuB,IAAI;IAE3B,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,SAAmB,EAAE;IAC3B,IAAI,eAAe;IAEnB,KAAK,MAAM,QAAQ,MAAO;QACxB,IACE,aAAa,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,gBACxC,aAAa,MAAM,GAAG,GACtB;YACA,OAAO,IAAI,CAAC,aAAa,IAAI;YAC7B,eAAe;QACjB,OAAO;YACL,gBAAgB,CAAC,eAAe,OAAO,EAAE,IAAI;QAC/C;IACF;IAEA,IAAI,aAAa,IAAI,IAAI;QACvB,OAAO,IAAI,CAAC,aAAa,IAAI;IAC/B;IAEA,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,GAAG;AACjD;AAGO,SAAS,mBACd,OAAe,EACf,QAAgB;IAEhB,MAAM,UAAoB,EAAE;IAE5B,IAAI;QACF,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,4CAA4C;gBAC5C,MAAM,aAAa;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,WAAY;oBAChC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;YAEF,KAAK;gBACH,0CAA0C;gBAC1C,MAAM,aAAa;oBACjB;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,WAAY;oBAChC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;YAEF,KAAK;gBACH,sCAAsC;gBACtC,MAAM,eAAe;oBACnB;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,aAAc;oBAClC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;QACJ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,OAAO;WAAI,IAAI,IAAI;KAAS,EAAE,oBAAoB;AACpD;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/navigation/navbar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Code, Settings, Home, Github } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nexport function Navbar() {\n  const pathname = usePathname()\n\n  const navigation = [\n    { name: 'Projects', href: '/', icon: Home },\n    { name: 'Settings', href: '/settings', icon: Settings },\n  ]\n\n  return (\n    <nav className=\"border-b border-slate-200/50 dark:border-slate-700/50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-slate-900/60 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center gap-8\">\n            <Link href=\"/\" className=\"flex items-center gap-3 group\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200\">\n                <Code className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"font-bold text-xl bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent\">\n                Code Index\n              </span>\n            </Link>\n\n            <div className=\"hidden md:flex items-center gap-1\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                const isActive = pathname === item.href\n\n                return (\n                  <Link key={item.name} href={item.href}>\n                    <Button\n                      variant={isActive ? 'secondary' : 'ghost'}\n                      size=\"sm\"\n                      className={cn(\n                        'flex items-center gap-2 transition-all duration-200',\n                        isActive\n                          ? 'bg-blue-50 dark:bg-blue-950/50 text-blue-700 dark:text-blue-300 shadow-sm'\n                          : 'hover:bg-slate-50 dark:hover:bg-slate-800'\n                      )}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                      {item.name}\n                    </Button>\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-4\">\n            <div className=\"hidden sm:flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span>AI-Powered Analysis</span>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"hidden sm:flex border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                asChild\n              >\n                <a href=\"https://github.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n                  <Github className=\"h-4 w-4 mr-2\" />\n                  GitHub\n                </a>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,sMAAA,CAAA,OAAI;QAAC;QAC1C;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAK,WAAU;kDAAqI;;;;;;;;;;;;0CAKvJ,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAAiB,MAAM,KAAK,IAAI;kDACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,cAAc;4CAClC,MAAK;4CACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uDACA,WACI,8EACA;;8DAGN,6LAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAZH,KAAK,IAAI;;;;;gCAgBxB;;;;;;;;;;;;kCAIJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,6LAAC;wCAAE,MAAK;wCAAqB,QAAO;wCAAS,KAAI;;0DAC/C,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAxEgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-12 w-full items-center justify-between rounded-xl border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 px-4 py-3 text-sm shadow-sm transition-all hover:border-slate-300 dark:hover:border-slate-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-800 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50 transition-transform data-[state=open]:rotate-180\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-xl border border-slate-200 dark:border-slate-700 bg-white/95 dark:bg-slate-800/95 backdrop-blur-xl text-slate-900 dark:text-slate-100 shadow-xl shadow-slate-900/10 dark:shadow-slate-900/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-2\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-lg py-3 pl-10 pr-3 text-sm outline-none transition-colors hover:bg-slate-100 dark:hover:bg-slate-700/50 focus:bg-blue-50 dark:focus:bg-blue-950/50 focus:text-blue-900 dark:focus:text-blue-100 data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-3 flex h-4 w-4 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6kBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uUACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/settings/settings-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { ArrowLeft, Save, Eye, EyeOff, CheckCircle, XCircle, Zap, Sparkles, RefreshCw } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Navbar } from '@/components/navigation/navbar'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport Link from 'next/link'\n\ninterface Provider {\n  name: string\n  displayName: string\n  isConfigured: boolean\n  description: string\n  websiteUrl: string\n}\n\ninterface Model {\n  id: string\n  name: string\n  description: string\n  pricing: {\n    prompt: string\n    completion: string\n  }\n  contextLength: number\n  isFree: boolean\n  provider: string\n}\n\nexport function SettingsPage() {\n  const [providers, setProviders] = useState<Provider[]>([])\n  const [apiKeys, setApiKeys] = useState<Record<string, string>>({})\n  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({})\n  const [isLoading, setIsLoading] = useState(true)\n  const [isSaving, setIsSaving] = useState(false)\n  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')\n  const [models, setModels] = useState<Model[]>([])\n  const [selectedModel, setSelectedModel] = useState<string>('qwen/qwen-2.5-72b-instruct:free')\n  const [isLoadingModels, setIsLoadingModels] = useState(false)\n\n  useEffect(() => {\n    fetchProviders()\n    fetchModels()\n  }, [])\n\n  const fetchProviders = async () => {\n    try {\n      const response = await fetch('/api/settings/providers')\n      if (response.ok) {\n        const data = await response.json()\n        setProviders(data.providers || [])\n      }\n    } catch (error) {\n      console.error('Error fetching providers:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const fetchModels = async () => {\n    setIsLoadingModels(true)\n    try {\n      const response = await fetch('/api/settings/models')\n      if (response.ok) {\n        const data = await response.json()\n        setModels(data.models || [])\n      }\n    } catch (error) {\n      console.error('Error fetching models:', error)\n    } finally {\n      setIsLoadingModels(false)\n    }\n  }\n\n  const handleApiKeyChange = (provider: string, value: string) => {\n    setApiKeys(prev => ({ ...prev, [provider]: value }))\n  }\n\n  const toggleShowKey = (provider: string) => {\n    setShowKeys(prev => ({ ...prev, [provider]: !prev[provider] }))\n  }\n\n  const saveSettings = async () => {\n    setIsSaving(true)\n    setSaveStatus('idle')\n\n    try {\n      // Save API keys\n      const providersResponse = await fetch('/api/settings/providers', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ apiKeys }),\n      })\n\n      // Save model selection\n      const selectedModelData = models.find(m => m.id === selectedModel)\n      const modelsResponse = await fetch('/api/settings/models', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          selectedModel,\n          provider: selectedModelData?.provider\n        }),\n      })\n\n      if (providersResponse.ok && modelsResponse.ok) {\n        setSaveStatus('success')\n        fetchProviders() // Refresh provider status\n        setTimeout(() => setSaveStatus('idle'), 3000)\n      } else {\n        setSaveStatus('error')\n      }\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      setSaveStatus('error')\n    } finally {\n      setIsSaving(false)\n    }\n  }\n\n  const getProviderInfo = (name: string) => {\n    const info = {\n      openai: {\n        description: 'GPT-4 and other OpenAI models',\n        websiteUrl: 'https://platform.openai.com/api-keys',\n      },\n      anthropic: {\n        description: 'Claude 3 and other Anthropic models',\n        websiteUrl: 'https://console.anthropic.com/',\n      },\n      google: {\n        description: 'Gemini and other Google AI models',\n        websiteUrl: 'https://makersuite.google.com/app/apikey',\n      },\n      openrouter: {\n        description: 'Access to multiple AI models through one API',\n        websiteUrl: 'https://openrouter.ai/keys',\n      },\n    }\n    return info[name as keyof typeof info] || { description: '', websiteUrl: '' }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p>Loading settings...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800\">\n      <Navbar />\n      {/* Header */}\n      <div className=\"border-b border-slate-200/50 dark:border-slate-700/50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl\">\n        <div className=\"container mx-auto px-4 py-8\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-6\">\n              <Link href=\"/\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"hover:bg-slate-100 dark:hover:bg-slate-800\">\n                  <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                  Back\n                </Button>\n              </Link>\n              <div>\n                <h1 className=\"text-4xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-purple-900 dark:from-slate-100 dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent\">\n                  Settings\n                </h1>\n                <p className=\"text-slate-600 dark:text-slate-300 mt-2 text-lg\">\n                  Configure AI providers and other settings\n                </p>\n              </div>\n            </div>\n            <Button\n              onClick={saveSettings}\n              disabled={isSaving}\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200\"\n            >\n              {isSaving ? (\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              ) : (\n                <Save className=\"w-4 h-4 mr-2\" />\n              )}\n              Save Settings\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-4xl mx-auto space-y-8\">\n          {/* Save Status */}\n          {saveStatus !== 'idle' && (\n            <div className={`flex items-center gap-3 p-4 rounded-xl border shadow-sm ${\n              saveStatus === 'success'\n                ? 'bg-emerald-50 dark:bg-emerald-950/20 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800'\n                : 'bg-red-50 dark:bg-red-950/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800'\n            }`}>\n              {saveStatus === 'success' ? (\n                <CheckCircle className=\"w-5 h-5\" />\n              ) : (\n                <XCircle className=\"w-5 h-5\" />\n              )}\n              <span className=\"font-medium\">\n                {saveStatus === 'success'\n                  ? 'Settings saved successfully!'\n                  : 'Failed to save settings. Please try again.'\n                }\n              </span>\n            </div>\n          )}\n\n          {/* AI Providers */}\n          <Card className=\"border-slate-200 dark:border-slate-700 shadow-lg\">\n            <CardHeader className=\"pb-6\">\n              <CardTitle className=\"flex items-center gap-3 text-xl\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center\">\n                  <Settings className=\"w-5 h-5 text-white\" />\n                </div>\n                AI Providers\n              </CardTitle>\n              <CardDescription className=\"text-base\">\n                Configure API keys for different AI providers. You need at least one configured provider to use the chat feature.\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-6\">\n              {providers.map((provider) => {\n                const info = getProviderInfo(provider.name)\n                return (\n                  <div key={provider.name} className=\"border border-slate-200 dark:border-slate-700 rounded-xl p-6 bg-white dark:bg-slate-800/50 hover:shadow-md transition-shadow\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"flex items-center gap-3\">\n                        <h3 className=\"font-semibold text-lg text-slate-900 dark:text-slate-100\">{provider.displayName}</h3>\n                        <Badge\n                          variant={provider.isConfigured ? 'default' : 'secondary'}\n                          className={provider.isConfigured\n                            ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800'\n                            : 'bg-slate-100 text-slate-700 dark:bg-slate-700 dark:text-slate-300'\n                          }\n                        >\n                          {provider.isConfigured ? 'Configured' : 'Not Configured'}\n                        </Badge>\n                      </div>\n                      <a\n                        href={info.websiteUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"text-sm text-blue-600 dark:text-blue-400 hover:underline font-medium\"\n                      >\n                        Get API Key →\n                      </a>\n                    </div>\n\n                    <p className=\"text-sm text-slate-600 dark:text-slate-400 mb-4 leading-relaxed\">\n                      {info.description}\n                    </p>\n\n                    <div className=\"flex gap-3\">\n                      <div className=\"flex-1 relative\">\n                        <Input\n                          type={showKeys[provider.name] ? 'text' : 'password'}\n                          placeholder={`Enter ${provider.displayName} API key`}\n                          value={apiKeys[provider.name] || ''}\n                          onChange={(e) => handleApiKeyChange(provider.name, e.target.value)}\n                          className=\"h-12 rounded-xl border-slate-200 dark:border-slate-700\"\n                        />\n                      </div>\n                      <Button\n                        type=\"button\"\n                        variant=\"outline\"\n                        size=\"icon\"\n                        onClick={() => toggleShowKey(provider.name)}\n                        className=\"h-12 w-12 rounded-xl border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                      >\n                        {showKeys[provider.name] ? (\n                          <EyeOff className=\"w-4 h-4\" />\n                        ) : (\n                          <Eye className=\"w-4 h-4\" />\n                        )}\n                      </Button>\n                    </div>\n                  </div>\n                )\n              })}\n            </CardContent>\n          </Card>\n\n          {/* Model Selection */}\n          <Card className=\"border-slate-200 dark:border-slate-700 shadow-lg\">\n            <CardHeader className=\"pb-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <CardTitle className=\"flex items-center gap-3 text-xl\">\n                    <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center\">\n                      <Zap className=\"w-5 h-5 text-white\" />\n                    </div>\n                    AI Model Selection\n                  </CardTitle>\n                  <CardDescription className=\"mt-2 text-base\">\n                    Choose the AI model for chat interactions. Free models are recommended to start.\n                  </CardDescription>\n                </div>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={fetchModels}\n                  disabled={isLoadingModels}\n                  className=\"hover:bg-slate-50 dark:hover:bg-slate-800\"\n                >\n                  {isLoadingModels ? (\n                    <RefreshCw className=\"w-4 h-4 mr-2 animate-spin\" />\n                  ) : (\n                    <RefreshCw className=\"w-4 h-4 mr-2\" />\n                  )}\n                  Refresh Models\n                </Button>\n              </div>\n            </CardHeader>\n            <CardContent className=\"space-y-8\">\n              <div className=\"space-y-4\">\n                <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300 block\">\n                  Select AI Model\n                </label>\n                <Select value={selectedModel} onValueChange={setSelectedModel}>\n                  <SelectTrigger className=\"w-full\">\n                    <SelectValue placeholder=\"Choose an AI model\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {models.length === 0 ? (\n                      <SelectItem value=\"loading\" disabled>\n                        {isLoadingModels ? 'Loading models...' : 'No models available'}\n                      </SelectItem>\n                    ) : (\n                      models.map((model) => (\n                        <SelectItem key={model.id} value={model.id}>\n                          <div className=\"flex items-center justify-between w-full\">\n                            <div className=\"flex items-center gap-3\">\n                              <span className=\"font-medium text-slate-900 dark:text-slate-100\">{model.name}</span>\n                              {model.isFree && (\n                                <Badge variant=\"secondary\" className=\"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800\">\n                                  <Sparkles className=\"w-3 h-3 mr-1\" />\n                                  Free\n                                </Badge>\n                              )}\n                            </div>\n                          </div>\n                        </SelectItem>\n                      ))\n                    )}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Selected Model Details */}\n              {selectedModel && models.length > 0 && (\n                <div className=\"border border-slate-200 dark:border-slate-700 rounded-xl p-6 bg-gradient-to-br from-slate-50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50\">\n                  {(() => {\n                    const model = models.find(m => m.id === selectedModel)\n                    if (!model) return null\n\n                    return (\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <h4 className=\"font-semibold text-lg text-slate-900 dark:text-slate-100\">{model.name}</h4>\n                          <div className=\"flex items-center gap-2\">\n                            <Badge variant=\"outline\" className=\"border-slate-300 dark:border-slate-600\">{model.provider}</Badge>\n                            {model.isFree && (\n                              <Badge className=\"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800\">\n                                <Sparkles className=\"w-3 h-3 mr-1\" />\n                                Free\n                              </Badge>\n                            )}\n                          </div>\n                        </div>\n\n                        {model.description && (\n                          <p className=\"text-sm text-slate-700 dark:text-slate-300 leading-relaxed\">\n                            {model.description}\n                          </p>\n                        )}\n\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 pt-2\">\n                          <div className=\"space-y-1\">\n                            <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Context Length</span>\n                            <div className=\"font-semibold text-slate-900 dark:text-slate-100\">\n                              {model.contextLength.toLocaleString()} tokens\n                            </div>\n                          </div>\n                          <div className=\"space-y-1\">\n                            <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Input Cost</span>\n                            <div className=\"font-semibold text-slate-900 dark:text-slate-100\">\n                              {model.isFree ? 'Free' : `$${model.pricing.prompt}/1K tokens`}\n                            </div>\n                          </div>\n                          <div className=\"space-y-1\">\n                            <span className=\"text-sm font-medium text-slate-600 dark:text-slate-400\">Output Cost</span>\n                            <div className=\"font-semibold text-slate-900 dark:text-slate-100\">\n                              {model.isFree ? 'Free' : `$${model.pricing.completion}/1K tokens`}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    )\n                  })()}\n                </div>\n              )}\n\n              {/* Recommended Models */}\n              <div className=\"space-y-4\">\n                <h4 className=\"font-semibold text-lg text-slate-900 dark:text-slate-100\">Recommended Models</h4>\n                <div className=\"grid gap-4\">\n                  <div className=\"flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-xl bg-white dark:bg-slate-800/50 hover:shadow-md transition-shadow\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-lg flex items-center justify-center\">\n                        <Sparkles className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <div>\n                        <div className=\"font-semibold text-slate-900 dark:text-slate-100\">Qwen 2.5 72B Instruct</div>\n                        <div className=\"text-sm text-slate-600 dark:text-slate-400\">\n                          High-quality free model, great for code analysis\n                        </div>\n                        <Badge variant=\"secondary\" className=\"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800 mt-1\">\n                          Free\n                        </Badge>\n                      </div>\n                    </div>\n                    <Button\n                      variant={selectedModel === 'qwen/qwen-2.5-72b-instruct:free' ? 'default' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setSelectedModel('qwen/qwen-2.5-72b-instruct:free')}\n                      disabled={selectedModel === 'qwen/qwen-2.5-72b-instruct:free'}\n                      className={selectedModel === 'qwen/qwen-2.5-72b-instruct:free' ? 'bg-emerald-600 hover:bg-emerald-700' : ''}\n                    >\n                      {selectedModel === 'qwen/qwen-2.5-72b-instruct:free' ? 'Selected' : 'Select'}\n                    </Button>\n                  </div>\n\n                  <div className=\"flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-xl bg-white dark:bg-slate-800/50 hover:shadow-md transition-shadow\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center\">\n                        <Zap className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <div>\n                        <div className=\"font-semibold text-slate-900 dark:text-slate-100\">GPT-4o Mini</div>\n                        <div className=\"text-sm text-slate-600 dark:text-slate-400\">\n                          Fast and affordable, requires OpenAI API key\n                        </div>\n                        <Badge variant=\"outline\" className=\"border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300 mt-1\">\n                          Paid\n                        </Badge>\n                      </div>\n                    </div>\n                    <Button\n                      variant={selectedModel === 'gpt-4o-mini' ? 'default' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setSelectedModel('gpt-4o-mini')}\n                      disabled={selectedModel === 'gpt-4o-mini'}\n                      className={selectedModel === 'gpt-4o-mini' ? 'bg-blue-600 hover:bg-blue-700' : ''}\n                    >\n                      {selectedModel === 'gpt-4o-mini' ? 'Selected' : 'Select'}\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Vector Database */}\n          <Card className=\"border-slate-200 dark:border-slate-700 shadow-lg\">\n            <CardHeader className=\"pb-6\">\n              <CardTitle className=\"flex items-center gap-3 text-xl\">\n                <div className=\"w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center\">\n                  <Settings className=\"w-5 h-5 text-white\" />\n                </div>\n                Vector Database\n              </CardTitle>\n              <CardDescription className=\"text-base\">\n                Configure Upstash Vector for code indexing and search\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-6\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">Upstash Vector REST URL</label>\n                  <Input\n                    placeholder=\"https://your-vector-db.upstash.io\"\n                    className=\"h-12 rounded-xl border-slate-200 dark:border-slate-700\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">Upstash Vector REST Token</label>\n                  <Input\n                    type=\"password\"\n                    placeholder=\"Your vector database token\"\n                    className=\"h-12 rounded-xl border-slate-200 dark:border-slate-700\"\n                  />\n                </div>\n                <div className=\"p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-xl\">\n                  <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n                    Get your Upstash Vector credentials from{' '}\n                    <a\n                      href=\"https://console.upstash.com/\"\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"font-medium underline hover:no-underline\"\n                    >\n                      Upstash Console →\n                    </a>\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;;;AAhBA;;;;;;;;;;AAuCO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;YACA;QACF;iCAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,aAAa,KAAK,SAAS,IAAI,EAAE;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,mBAAmB;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU,KAAK,MAAM,IAAI,EAAE;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE;YAAM,CAAC;IACpD;IAEA,MAAM,gBAAgB,CAAC;QACrB,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;YAAC,CAAC;IAC/D;IAEA,MAAM,eAAe;QACnB,YAAY;QACZ,cAAc;QAEd,IAAI;YACF,gBAAgB;YAChB,MAAM,oBAAoB,MAAM,MAAM,2BAA2B;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAQ;YACjC;YAEA,uBAAuB;YACvB,MAAM,oBAAoB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpD,MAAM,iBAAiB,MAAM,MAAM,wBAAwB;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,UAAU,mBAAmB;gBAC/B;YACF;YAEA,IAAI,kBAAkB,EAAE,IAAI,eAAe,EAAE,EAAE;gBAC7C,cAAc;gBACd,iBAAiB,0BAA0B;;gBAC3C,WAAW,IAAM,cAAc,SAAS;YAC1C,OAAO;gBACL,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,cAAc;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO;YACX,QAAQ;gBACN,aAAa;gBACb,YAAY;YACd;YACA,WAAW;gBACT,aAAa;gBACb,YAAY;YACd;YACA,QAAQ;gBACN,aAAa;gBACb,YAAY;YACd;YACA,YAAY;gBACV,aAAa;gBACb,YAAY;YACd;QACF;QACA,OAAO,IAAI,CAAC,KAA0B,IAAI;YAAE,aAAa;YAAI,YAAY;QAAG;IAC9E;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;;8DAC1C,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI1C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuK;;;;;;0DAGrL,6LAAC;gDAAE,WAAU;0DAAkD;;;;;;;;;;;;;;;;;;0CAKnE,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;oCAET,yBACC,6LAAC;wCAAI,WAAU;;;;;6DAEf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAChB;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,eAAe,wBACd,6LAAC;4BAAI,WAAW,CAAC,wDAAwD,EACvE,eAAe,YACX,2HACA,kGACJ;;gCACC,eAAe,0BACd,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CAErB,6LAAC;oCAAK,WAAU;8CACb,eAAe,YACZ,iCACA;;;;;;;;;;;;sCAOV,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAS,WAAU;;;;;;;;;;;gDAChB;;;;;;;sDAGR,6LAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAY;;;;;;;;;;;;8CAIzC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,UAAU,GAAG,CAAC,CAAC;wCACd,MAAM,OAAO,gBAAgB,SAAS,IAAI;wCAC1C,qBACE,6LAAC;4CAAwB,WAAU;;8DACjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA4D,SAAS,WAAW;;;;;;8EAC9F,6LAAC,oIAAA,CAAA,QAAK;oEACJ,SAAS,SAAS,YAAY,GAAG,YAAY;oEAC7C,WAAW,SAAS,YAAY,GAC5B,4HACA;8EAGH,SAAS,YAAY,GAAG,eAAe;;;;;;;;;;;;sEAG5C,6LAAC;4DACC,MAAM,KAAK,UAAU;4DACrB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEACX;;;;;;;;;;;;8DAKH,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAGnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gEACJ,MAAM,QAAQ,CAAC,SAAS,IAAI,CAAC,GAAG,SAAS;gEACzC,aAAa,CAAC,MAAM,EAAE,SAAS,WAAW,CAAC,QAAQ,CAAC;gEACpD,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI;gEACjC,UAAU,CAAC,IAAM,mBAAmB,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;gEACjE,WAAU;;;;;;;;;;;sEAGd,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,cAAc,SAAS,IAAI;4DAC1C,WAAU;sEAET,QAAQ,CAAC,SAAS,IAAI,CAAC,iBACtB,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;2CAhDb,SAAS,IAAI;;;;;oCAsD3B;;;;;;;;;;;;sCAKJ,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;;;;;;4DACX;;;;;;;kEAGR,6LAAC,mIAAA,CAAA,kBAAe;wDAAC,WAAU;kEAAiB;;;;;;;;;;;;0DAI9C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,UAAU;gDACV,WAAU;;oDAET,gCACC,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;6EAErB,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDACrB;;;;;;;;;;;;;;;;;;8CAKR,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAiE;;;;;;8DAGlF,6LAAC,qIAAA,CAAA,SAAM;oDAAC,OAAO;oDAAe,eAAe;;sEAC3C,6LAAC,qIAAA,CAAA,gBAAa;4DAAC,WAAU;sEACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,6LAAC,qIAAA,CAAA,gBAAa;sEACX,OAAO,MAAM,KAAK,kBACjB,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;gEAAU,QAAQ;0EACjC,kBAAkB,sBAAsB;;;;;uEAG3C,OAAO,GAAG,CAAC,CAAC,sBACV,6LAAC,qIAAA,CAAA,aAAU;oEAAgB,OAAO,MAAM,EAAE;8EACxC,cAAA,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAkD,MAAM,IAAI;;;;;;gFAC3E,MAAM,MAAM,kBACX,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAY,WAAU;;sGACnC,6LAAC,6MAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;mEAN9B,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;wCAoBlC,iBAAiB,OAAO,MAAM,GAAG,mBAChC,6LAAC;4CAAI,WAAU;sDACZ,CAAC;gDACA,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gDACxC,IAAI,CAAC,OAAO,OAAO;gDAEnB,qBACE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA4D,MAAM,IAAI;;;;;;8EACpF,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAA0C,MAAM,QAAQ;;;;;;wEAC1F,MAAM,MAAM,kBACX,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;;8FACf,6LAAC,6MAAA,CAAA,WAAQ;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;wDAO5C,MAAM,WAAW,kBAChB,6LAAC;4DAAE,WAAU;sEACV,MAAM,WAAW;;;;;;sEAItB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAyD;;;;;;sFACzE,6LAAC;4EAAI,WAAU;;gFACZ,MAAM,aAAa,CAAC,cAAc;gFAAG;;;;;;;;;;;;;8EAG1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAyD;;;;;;sFACzE,6LAAC;4EAAI,WAAU;sFACZ,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC;;;;;;;;;;;;8EAGjE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAyD;;;;;;sFACzE,6LAAC;4EAAI,WAAU;sFACZ,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,EAAE,MAAM,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;4CAM7E,CAAC;;;;;;sDAKL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA2D;;;;;;8DACzE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;;;;;;sFAEtB,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;8FAAmD;;;;;;8FAClE,6LAAC;oFAAI,WAAU;8FAA6C;;;;;;8FAG5D,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAY,WAAU;8FAA+H;;;;;;;;;;;;;;;;;;8EAKxK,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAS,kBAAkB,oCAAoC,YAAY;oEAC3E,MAAK;oEACL,SAAS,IAAM,iBAAiB;oEAChC,UAAU,kBAAkB;oEAC5B,WAAW,kBAAkB,oCAAoC,wCAAwC;8EAExG,kBAAkB,oCAAoC,aAAa;;;;;;;;;;;;sEAIxE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;sFAEjB,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;8FAAmD;;;;;;8FAClE,6LAAC;oFAAI,WAAU;8FAA6C;;;;;;8FAG5D,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAU,WAAU;8FAA6E;;;;;;;;;;;;;;;;;;8EAKpH,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAS,kBAAkB,gBAAgB,YAAY;oEACvD,MAAK;oEACL,SAAS,IAAM,iBAAiB;oEAChC,UAAU,kBAAkB;oEAC5B,WAAW,kBAAkB,gBAAgB,kCAAkC;8EAE9E,kBAAkB,gBAAgB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS5D,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAS,WAAU;;;;;;;;;;;gDAChB;;;;;;;sDAGR,6LAAC,mIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAY;;;;;;;;;;;;8CAIzC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2D;;;;;;kEAC5E,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2D;;;;;;kEAC5E,6LAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;wDAA2C;wDACb;sEACzC,6LAAC;4DACC,MAAK;4DACL,QAAO;4DACP,KAAI;4DACJ,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GAhfgB;KAAA", "debugId": null}}]}