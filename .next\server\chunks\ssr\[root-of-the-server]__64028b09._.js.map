{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport * as path from \"path\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// File extension utilities\nexport function getFileExtension(filePath: string): string {\n  return path.extname(filePath).toLowerCase();\n}\n\nexport function getLanguageFromExtension(extension: string): string {\n  const languageMap: Record<string, string> = {\n    \".js\": \"javascript\",\n    \".jsx\": \"javascript\",\n    \".ts\": \"typescript\",\n    \".tsx\": \"typescript\",\n    \".py\": \"python\",\n    \".java\": \"java\",\n    \".cpp\": \"cpp\",\n    \".c\": \"c\",\n    \".cs\": \"csharp\",\n    \".php\": \"php\",\n    \".rb\": \"ruby\",\n    \".go\": \"go\",\n    \".rs\": \"rust\",\n    \".swift\": \"swift\",\n    \".kt\": \"kotlin\",\n    \".scala\": \"scala\",\n    \".sh\": \"bash\",\n    \".sql\": \"sql\",\n    \".html\": \"html\",\n    \".css\": \"css\",\n    \".scss\": \"scss\",\n    \".sass\": \"sass\",\n    \".less\": \"less\",\n    \".vue\": \"vue\",\n    \".svelte\": \"svelte\",\n    \".md\": \"markdown\",\n    \".json\": \"json\",\n    \".xml\": \"xml\",\n    \".yaml\": \"yaml\",\n    \".yml\": \"yaml\",\n    \".toml\": \"toml\",\n    \".ini\": \"ini\",\n    \".cfg\": \"ini\",\n    \".conf\": \"ini\",\n  };\n\n  return languageMap[extension] || \"text\";\n}\n\n// File filtering\nconst SKIP_EXTENSIONS = [\n  // Images\n  \".png\",\n  \".jpg\",\n  \".jpeg\",\n  \".gif\",\n  \".svg\",\n  \".ico\",\n  \".webp\",\n  \".bmp\",\n  \".tiff\",\n  // Documents\n  \".pdf\",\n  \".doc\",\n  \".docx\",\n  \".xls\",\n  \".xlsx\",\n  \".ppt\",\n  \".pptx\",\n  // Archives\n  \".zip\",\n  \".tar\",\n  \".gz\",\n  \".rar\",\n  \".7z\",\n  \".bz2\",\n  \".xz\",\n  // Media\n  \".mp3\",\n  \".mp4\",\n  \".avi\",\n  \".mov\",\n  \".wmv\",\n  \".flv\",\n  \".mkv\",\n  \".wav\",\n  \".ogg\",\n  // Executables\n  \".exe\",\n  \".dll\",\n  \".so\",\n  \".dylib\",\n  \".app\",\n  \".deb\",\n  \".rpm\",\n  \".msi\",\n  // Temporary/Cache\n  \".log\",\n  \".tmp\",\n  \".cache\",\n  \".lock\",\n  \".pid\",\n  \".swp\",\n  \".swo\",\n  \".bak\",\n  // Font files\n  \".ttf\",\n  \".otf\",\n  \".woff\",\n  \".woff2\",\n  \".eot\",\n  // Database files\n  \".db\",\n  \".sqlite\",\n  \".sqlite3\",\n];\n\nconst SKIP_PATTERNS = [\n  // Dependencies\n  \"node_modules\",\n  \"vendor\",\n  \"__pycache__\",\n  \".pytest_cache\",\n  // Version control\n  \".git\",\n  \".svn\",\n  \".hg\",\n  // Build outputs\n  \".next\",\n  \".nuxt\",\n  \"dist\",\n  \"build\",\n  \"out\",\n  \"target\",\n  \"bin\",\n  \"obj\",\n  // IDE/Editor\n  \".vscode\",\n  \".idea\",\n  \".vs\",\n  \".sublime-project\",\n  \".sublime-workspace\",\n  // Testing/Coverage\n  \"coverage\",\n  \".nyc_output\",\n  \".coverage\",\n  \"htmlcov\",\n  // Lock files\n  \"package-lock.json\",\n  \"yarn.lock\",\n  \"pnpm-lock.yaml\",\n  \"composer.lock\",\n  \"Pipfile.lock\",\n  // Environment files\n  \".env\",\n  \".env.local\",\n  \".env.development\",\n  \".env.production\",\n  \".env.staging\",\n  // OS files\n  \".DS_Store\",\n  \"Thumbs.db\",\n  \"desktop.ini\",\n  // Logs\n  \"logs\",\n  \"*.log\",\n  // Cache directories\n  \".cache\",\n  \".parcel-cache\",\n  \".webpack\",\n  \".rollup.cache\",\n];\n\nconst SKIP_FILENAMES = [\n  // Lock files (exact matches)\n  \"package-lock.json\",\n  \"yarn.lock\",\n  \"pnpm-lock.yaml\",\n  \"composer.lock\",\n  \"Pipfile.lock\",\n  // Environment files\n  \".env\",\n  \".env.local\",\n  \".env.development\",\n  \".env.production\",\n  \".env.staging\",\n  // OS files\n  \".DS_Store\",\n  \"Thumbs.db\",\n  \"desktop.ini\",\n  // IDE files\n  \".gitignore\",\n  \".gitattributes\",\n  \".editorconfig\",\n];\n\nconst CODE_EXTENSIONS = [\n  // Web technologies\n  \".js\",\n  \".jsx\",\n  \".ts\",\n  \".tsx\",\n  \".vue\",\n  \".svelte\",\n  \".astro\",\n  \".html\",\n  \".htm\",\n  \".css\",\n  \".scss\",\n  \".sass\",\n  \".less\",\n  \".stylus\",\n  // Programming languages\n  \".py\",\n  \".java\",\n  \".cpp\",\n  \".c\",\n  \".cs\",\n  \".php\",\n  \".rb\",\n  \".go\",\n  \".rs\",\n  \".swift\",\n  \".kt\",\n  \".scala\",\n  \".clj\",\n  \".cljs\",\n  \".hs\",\n  \".elm\",\n  \".dart\",\n  \".lua\",\n  \".perl\",\n  \".r\",\n  \".matlab\",\n  \".julia\",\n  \".f90\",\n  \".f95\",\n  // Shell/Scripts\n  \".sh\",\n  \".bash\",\n  \".zsh\",\n  \".fish\",\n  \".ps1\",\n  \".bat\",\n  \".cmd\",\n  // Data/Config\n  \".json\",\n  \".xml\",\n  \".yaml\",\n  \".yml\",\n  \".toml\",\n  \".ini\",\n  \".cfg\",\n  \".conf\",\n  \".properties\",\n  \".env.example\",\n  \".env.template\",\n  // Database\n  \".sql\",\n  \".graphql\",\n  \".gql\",\n  // Documentation\n  \".md\",\n  \".mdx\",\n  \".txt\",\n  \".rst\",\n  \".adoc\",\n  // Other\n  \".dockerfile\",\n  \".gitignore\",\n  \".gitattributes\",\n  \".editorconfig\",\n];\n\nfunction shouldSkipPath(filePath: string): boolean {\n  const normalizedPath = filePath.replace(/\\\\/g, \"/\");\n  const pathParts = normalizedPath.split(\"/\");\n\n  // Check if any part of the path matches skip patterns\n  for (const part of pathParts) {\n    for (const pattern of SKIP_PATTERNS) {\n      if (part === pattern || part.includes(pattern)) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n}\n\nexport function shouldUploadFile(filePath: string): boolean {\n  const extension = getFileExtension(filePath);\n  const fileName = path.basename(filePath);\n\n  // Skip files with unwanted extensions\n  if (SKIP_EXTENSIONS.includes(extension)) {\n    return false;\n  }\n\n  // Skip specific filenames\n  if (SKIP_FILENAMES.includes(fileName)) {\n    return false;\n  }\n\n  // Skip paths containing unwanted patterns\n  if (shouldSkipPath(filePath)) {\n    return false;\n  }\n\n  // Only allow files with code extensions or common project files\n  return (\n    CODE_EXTENSIONS.includes(extension) ||\n    fileName.toLowerCase().includes(\"readme\") ||\n    fileName.toLowerCase().includes(\"license\") ||\n    fileName.toLowerCase().includes(\"changelog\") ||\n    fileName.toLowerCase().includes(\"makefile\") ||\n    fileName === \"Dockerfile\" ||\n    fileName === \"docker-compose.yml\" ||\n    fileName === \"docker-compose.yaml\"\n  );\n}\n\nexport function shouldIndexFile(filePath: string): boolean {\n  // Use the same logic as shouldUploadFile for consistency\n  return shouldUploadFile(filePath);\n}\n\n// Code chunking\nexport function chunkCode(\n  content: string,\n  maxChunkSize: number = 1000\n): string[] {\n  const lines = content.split(\"\\n\");\n  const chunks: string[] = [];\n  let currentChunk = \"\";\n\n  for (const line of lines) {\n    if (\n      currentChunk.length + line.length + 1 > maxChunkSize &&\n      currentChunk.length > 0\n    ) {\n      chunks.push(currentChunk.trim());\n      currentChunk = line;\n    } else {\n      currentChunk += (currentChunk ? \"\\n\" : \"\") + line;\n    }\n  }\n\n  if (currentChunk.trim()) {\n    chunks.push(currentChunk.trim());\n  }\n\n  return chunks.filter((chunk) => chunk.length > 0);\n}\n\n// Code symbol extraction\nexport function extractCodeSymbols(\n  content: string,\n  language: string\n): string[] {\n  const symbols: string[] = [];\n\n  try {\n    switch (language) {\n      case \"javascript\":\n      case \"typescript\":\n        // Extract function names, class names, etc.\n        const jsPatterns = [\n          /function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n          /class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n          /const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /let\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /var\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:\\s*function/g,\n          /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=>\\s*/g,\n        ];\n\n        for (const pattern of jsPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n\n      case \"python\":\n        // Extract Python function and class names\n        const pyPatterns = [\n          /def\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n        ];\n\n        for (const pattern of pyPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n\n      case \"java\":\n        // Extract Java method and class names\n        const javaPatterns = [\n          /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /interface\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /public\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n          /private\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n          /protected\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n        ];\n\n        for (const pattern of javaPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n    }\n  } catch (error) {\n    console.error(\"Error extracting symbols:\", error);\n  }\n\n  return [...new Set(symbols)]; // Remove duplicates\n}\n\n// File size formatting\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return \"0 Bytes\";\n\n  const k = 1024;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE,UAAU,WAAW;AAC3C;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,cAAsC;QAC1C,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW;QACX,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IAEA,OAAO,WAAW,CAAC,UAAU,IAAI;AACnC;AAEA,iBAAiB;AACjB,MAAM,kBAAkB;IACtB,SAAS;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,YAAY;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,kBAAkB;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,iBAAiB;IACjB;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IACpB,eAAe;IACf;IACA;IACA;IACA;IACA,kBAAkB;IAClB;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,mBAAmB;IACnB;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,OAAO;IACP;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB,6BAA6B;IAC7B;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,YAAY;IACZ;IACA;IACA;CACD;AAED,MAAM,kBAAkB;IACtB,mBAAmB;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,wBAAwB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;IACA;IACA;IACA;CACD;AAED,SAAS,eAAe,QAAgB;IACtC,MAAM,iBAAiB,SAAS,OAAO,CAAC,OAAO;IAC/C,MAAM,YAAY,eAAe,KAAK,CAAC;IAEvC,sDAAsD;IACtD,KAAK,MAAM,QAAQ,UAAW;QAC5B,KAAK,MAAM,WAAW,cAAe;YACnC,IAAI,SAAS,WAAW,KAAK,QAAQ,CAAC,UAAU;gBAC9C,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,MAAM,YAAY,iBAAiB;IACnC,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,WAAa,AAAD,EAAE;IAE/B,sCAAsC;IACtC,IAAI,gBAAgB,QAAQ,CAAC,YAAY;QACvC,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,eAAe,QAAQ,CAAC,WAAW;QACrC,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,eAAe,WAAW;QAC5B,OAAO;IACT;IAEA,gEAAgE;IAChE,OACE,gBAAgB,QAAQ,CAAC,cACzB,SAAS,WAAW,GAAG,QAAQ,CAAC,aAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,cAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,gBAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,eAChC,aAAa,gBACb,aAAa,wBACb,aAAa;AAEjB;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,yDAAyD;IACzD,OAAO,iBAAiB;AAC1B;AAGO,SAAS,UACd,OAAe,EACf,eAAuB,IAAI;IAE3B,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,SAAmB,EAAE;IAC3B,IAAI,eAAe;IAEnB,KAAK,MAAM,QAAQ,MAAO;QACxB,IACE,aAAa,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,gBACxC,aAAa,MAAM,GAAG,GACtB;YACA,OAAO,IAAI,CAAC,aAAa,IAAI;YAC7B,eAAe;QACjB,OAAO;YACL,gBAAgB,CAAC,eAAe,OAAO,EAAE,IAAI;QAC/C;IACF;IAEA,IAAI,aAAa,IAAI,IAAI;QACvB,OAAO,IAAI,CAAC,aAAa,IAAI;IAC/B;IAEA,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,GAAG;AACjD;AAGO,SAAS,mBACd,OAAe,EACf,QAAgB;IAEhB,MAAM,UAAoB,EAAE;IAE5B,IAAI;QACF,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,4CAA4C;gBAC5C,MAAM,aAAa;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,WAAY;oBAChC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;YAEF,KAAK;gBACH,0CAA0C;gBAC1C,MAAM,aAAa;oBACjB;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,WAAY;oBAChC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;YAEF,KAAK;gBACH,sCAAsC;gBACtC,MAAM,eAAe;oBACnB;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,aAAc;oBAClC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;QACJ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,OAAO;WAAI,IAAI,IAAI;KAAS,EAAE,oBAAoB;AACpD;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2KACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/projects/create-project-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle\n} from '@/components/ui/dialog'\nimport { Badge } from '@/components/ui/badge'\nimport { FolderOpen, Loader2, Code } from 'lucide-react'\n\ninterface CreateProjectDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onProjectCreated: () => void\n}\n\nexport function CreateProjectDialog({ open, onOpenChange, onProjectCreated }: CreateProjectDialogProps) {\n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    path: '',\n    language: '',\n    framework: '',\n  })\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError('')\n\n    try {\n      const response = await fetch('/api/projects', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      })\n\n      if (response.ok) {\n        onProjectCreated()\n        onOpenChange(false)\n        setFormData({\n          name: '',\n          description: '',\n          path: '',\n          language: '',\n          framework: '',\n        })\n      } else {\n        const data = await response.json()\n        setError(data.error || 'Failed to create project')\n      }\n    } catch (error) {\n      setError('Failed to create project')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const selectFolder = async () => {\n    try {\n      // For now, we'll use a simple prompt. In a real app, you'd use a file picker\n      const path = prompt('Enter the path to your project folder:')\n      if (path) {\n        setFormData(prev => ({ ...prev, path }))\n        \n        // Auto-detect project name from path\n        if (!formData.name) {\n          const folderName = path.split(/[/\\\\]/).pop() || ''\n          setFormData(prev => ({ ...prev, name: folderName }))\n        }\n      }\n    } catch (error) {\n      console.error('Error selecting folder:', error)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"w-full max-w-lg bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-slate-200/50 dark:border-slate-700/50 shadow-2xl\">\n        <DialogHeader className=\"text-center pb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4\">\n            <Code className=\"w-6 h-6 text-white\" />\n          </div>\n          <DialogTitle className=\"text-2xl bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent\">\n            Create New Project\n          </DialogTitle>\n          <DialogDescription className=\"text-slate-600 dark:text-slate-400\">\n            Add a new code project to index and analyze with AI\n          </DialogDescription>\n        </DialogHeader>\n        <div className=\"pt-0\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {error && (\n              <div className=\"text-sm text-red-700 dark:text-red-400 bg-red-50 dark:bg-red-950/50 p-4 rounded-lg border border-red-200 dark:border-red-800\">\n                {error}\n              </div>\n            )}\n\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">\n                Project Name *\n              </label>\n              <Input\n                value={formData.name}\n                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                placeholder=\"My Awesome Project\"\n                required\n                className=\"bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">\n                Description\n              </label>\n              <Input\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                placeholder=\"A brief description of your project\"\n                className=\"bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">\n                Project Path *\n              </label>\n              <div className=\"flex gap-2\">\n                <Input\n                  value={formData.path}\n                  onChange={(e) => setFormData(prev => ({ ...prev, path: e.target.value }))}\n                  placeholder=\"/path/to/your/project\"\n                  required\n                  className=\"bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400\"\n                />\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={selectFolder}\n                  className=\"border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                >\n                  <FolderOpen className=\"w-4 h-4\" />\n                </Button>\n              </div>\n              <p className=\"text-xs text-slate-500 dark:text-slate-400\">\n                Enter the full path to your project directory\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">\n                  Language\n                </label>\n                <Input\n                  value={formData.language}\n                  onChange={(e) => setFormData(prev => ({ ...prev, language: e.target.value }))}\n                  placeholder=\"JavaScript\"\n                  className=\"bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400\"\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-semibold text-slate-700 dark:text-slate-300\">\n                  Framework\n                </label>\n                <Input\n                  value={formData.framework}\n                  onChange={(e) => setFormData(prev => ({ ...prev, framework: e.target.value }))}\n                  placeholder=\"React\"\n                  className=\"bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex gap-3 pt-6\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                className=\"flex-1 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"flex-1 bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200\"\n              >\n                {isLoading && <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />}\n                Create Project\n              </Button>\n            </div>\n          </form>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAbA;;;;;;;AAqBO,SAAS,oBAAoB,EAAE,IAAI,EAAE,YAAY,EAAE,gBAAgB,EAA4B;IACpG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,WAAW;IACb;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;gBACA,aAAa;gBACb,YAAY;oBACV,MAAM;oBACN,aAAa;oBACb,MAAM;oBACN,UAAU;oBACV,WAAW;gBACb;YACF,OAAO;gBACL,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,6EAA6E;YAC7E,MAAM,OAAO,OAAO;YACpB,IAAI,MAAM;gBACR,YAAY,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE;oBAAK,CAAC;gBAEtC,qCAAqC;gBACrC,IAAI,CAAC,SAAS,IAAI,EAAE;oBAClB,MAAM,aAAa,KAAK,KAAK,CAAC,SAAS,GAAG,MAAM;oBAChD,YAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,MAAM;wBAAW,CAAC;gBACpD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAA4H;;;;;;sCAGnJ,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAAqC;;;;;;;;;;;;8BAIpE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;4BACrC,uBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA2D;;;;;;kDAG5E,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,aAAY;wCACZ,QAAQ;wCACR,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA2D;;;;;;kDAG5E,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC9E,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA2D;;;;;;kDAG5E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACvE,aAAY;gDACZ,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG1B,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;0CAK5D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA2D;;;;;;0DAG5E,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,SAAS,QAAQ;gDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC3E,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA2D;;;;;;0DAG5E,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC5E,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU;wCACV,WAAU;;4CAET,2BAAa,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShF", "debugId": null}}, {"offset": {"line": 1204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/navigation/navbar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Code, Settings, Home, Github } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nexport function Navbar() {\n  const pathname = usePathname()\n\n  const navigation = [\n    { name: 'Projects', href: '/', icon: Home },\n    { name: 'Settings', href: '/settings', icon: Settings },\n  ]\n\n  return (\n    <nav className=\"border-b border-slate-200/50 dark:border-slate-700/50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-slate-900/60 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center gap-8\">\n            <Link href=\"/\" className=\"flex items-center gap-3 group\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200\">\n                <Code className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"font-bold text-xl bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent\">\n                Code Index\n              </span>\n            </Link>\n\n            <div className=\"hidden md:flex items-center gap-1\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                const isActive = pathname === item.href\n\n                return (\n                  <Link key={item.name} href={item.href}>\n                    <Button\n                      variant={isActive ? 'secondary' : 'ghost'}\n                      size=\"sm\"\n                      className={cn(\n                        'flex items-center gap-2 transition-all duration-200',\n                        isActive\n                          ? 'bg-blue-50 dark:bg-blue-950/50 text-blue-700 dark:text-blue-300 shadow-sm'\n                          : 'hover:bg-slate-50 dark:hover:bg-slate-800'\n                      )}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                      {item.name}\n                    </Button>\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-4\">\n            <div className=\"hidden sm:flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span>AI-Powered Analysis</span>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"hidden sm:flex border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                asChild\n              >\n                <a href=\"https://github.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n                  <Github className=\"h-4 w-4 mr-2\" />\n                  GitHub\n                </a>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,mMAAA,CAAA,OAAI;QAAC;QAC1C;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCAAK,WAAU;kDAAqI;;;;;;;;;;;;0CAKvJ,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wCAAiB,MAAM,KAAK,IAAI;kDACnC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,cAAc;4CAClC,MAAK;4CACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uDACA,WACI,8EACA;;8DAGN,8OAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAZH,KAAK,IAAI;;;;;gCAgBxB;;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,8OAAC;wCAAE,MAAK;wCAAqB,QAAO;wCAAS,KAAI;;0DAC/C,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/projects/projects-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Plus, Search, Code, Clock, FileText, Zap, Brain, Database, Sparkles, ArrowRight, Github, Star, Users, Rocket } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { CreateProjectDialog } from './create-project-dialog'\nimport { Navbar } from '@/components/navigation/navbar'\n\ninterface Project {\n  id: string\n  name: string\n  description?: string\n  path: string\n  language?: string\n  framework?: string\n  isIndexed: boolean\n  indexingStatus: string\n  totalFiles: number\n  indexedFiles: number\n  totalLines: number\n  createdAt: string\n  lastIndexedAt?: string\n}\n\nexport function ProjectsPage() {\n  const [projects, setProjects] = useState<Project[]>([])\n  const [searchQuery, setSearchQuery] = useState('')\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    fetchProjects()\n  }, [])\n\n  const fetchProjects = async () => {\n    try {\n      const response = await fetch('/api/projects')\n      if (response.ok) {\n        const data = await response.json()\n        setProjects(data.projects || [])\n      }\n    } catch (error) {\n      console.error('Error fetching projects:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const filteredProjects = projects.filter(project =>\n    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    project.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    project.language?.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'bg-green-500'\n      case 'indexing': return 'bg-blue-500'\n      case 'failed': return 'bg-red-500'\n      default: return 'bg-gray-500'\n    }\n  }\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed': return 'Indexed'\n      case 'indexing': return 'Indexing...'\n      case 'failed': return 'Failed'\n      default: return 'Pending'\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]\" />\n\n      {/* Content */}\n      <div className=\"relative z-10\">\n        <Navbar />\n\n        {/* Hero Section */}\n        {projects.length === 0 && !isLoading ? (\n          <div className=\"relative overflow-hidden\">\n            <div className=\"container mx-auto px-4 py-20\">\n            <div className=\"text-center max-w-4xl mx-auto\">\n              {/* Hero Content */}\n              <div className=\"mb-8 animate-fadeInUp\">\n                <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 text-blue-600 dark:text-blue-400 px-6 py-3 rounded-full text-sm font-medium mb-8 border border-blue-200/50 dark:border-blue-800/50 shadow-lg backdrop-blur-sm\">\n                  <Sparkles className=\"w-4 h-4\" />\n                  AI-Powered Code Analysis\n                </div>\n\n                <h1 className=\"text-5xl md:text-7xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-purple-900 dark:from-slate-100 dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent mb-8 leading-tight\">\n                  Understand Your\n                  <br />\n                  <span className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                    Codebase Instantly\n                  </span>\n                </h1>\n\n                <p className=\"text-xl text-slate-600 dark:text-slate-300 mb-10 max-w-3xl mx-auto leading-relaxed\">\n                  Index your code projects and chat with AI to understand functions, classes, and architecture.\n                  Get instant insights about your codebase with semantic search and intelligent analysis.\n                </p>\n\n                <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\">\n                  <Button\n                    size=\"lg\"\n                    className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 rounded-xl\"\n                    onClick={() => setIsCreateDialogOpen(true)}\n                  >\n                    <Plus className=\"w-5 h-5 mr-2\" />\n                    Create Your First Project\n                  </Button>\n                  <Button\n                    size=\"lg\"\n                    variant=\"outline\"\n                    className=\"px-10 py-4 text-lg font-semibold border-2 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\n                  >\n                    <Github className=\"w-5 h-5 mr-2\" />\n                    View on GitHub\n                  </Button>\n                </div>\n              </div>\n\n              {/* Features Grid */}\n              <div className=\"grid md:grid-cols-3 gap-8 mt-20\">\n                <div className=\"group bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-3xl p-8 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-2xl hover:border-blue-200 dark:hover:border-blue-800 transition-all duration-300 hover:-translate-y-2\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    <Brain className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 text-slate-900 dark:text-slate-100\">AI-Powered Chat</h3>\n                  <p className=\"text-slate-600 dark:text-slate-300 leading-relaxed\">\n                    Ask questions about your code and get intelligent responses from multiple AI providers.\n                  </p>\n                </div>\n\n                <div className=\"group bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-3xl p-8 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-2xl hover:border-purple-200 dark:hover:border-purple-800 transition-all duration-300 hover:-translate-y-2\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    <Database className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 text-slate-900 dark:text-slate-100\">Smart Indexing</h3>\n                  <p className=\"text-slate-600 dark:text-slate-300 leading-relaxed\">\n                    Automatically index your codebase with vector embeddings for semantic search.\n                  </p>\n                </div>\n\n                <div className=\"group bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-3xl p-8 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-2xl hover:border-green-200 dark:hover:border-green-800 transition-all duration-300 hover:-translate-y-2\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300\">\n                    <Rocket className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-3 text-slate-900 dark:text-slate-100\">Multi-Language</h3>\n                  <p className=\"text-slate-600 dark:text-slate-300 leading-relaxed\">\n                    Support for 20+ programming languages including JavaScript, Python, Java, and more.\n                  </p>\n                </div>\n              </div>\n\n              {/* Stats */}\n              <div className=\"flex justify-center items-center gap-8 mt-16 text-sm text-slate-500 dark:text-slate-400\">\n                <div className=\"flex items-center gap-2\">\n                  <Star className=\"w-4 h-4\" />\n                  <span>Open Source</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Users className=\"w-4 h-4\" />\n                  <span>Community Driven</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Code className=\"w-4 h-4\" />\n                  <span>Developer First</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        ) : (\n          /* Projects Section */\n          <div className=\"container mx-auto px-4 py-12\">\n            {/* Header */}\n            <div className=\"flex flex-col lg:flex-row lg:items-center justify-between mb-12 gap-6\">\n              <div className=\"animate-fadeInUp\">\n                <h1 className=\"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-purple-900 dark:from-slate-100 dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent mb-3\">\n                  Your Projects\n                </h1>\n                <p className=\"text-xl text-slate-600 dark:text-slate-400\">\n                  Manage and analyze your code projects with AI\n                </p>\n              </div>\n              <Button\n                onClick={() => setIsCreateDialogOpen(true)}\n                className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-8 py-3 text-lg font-semibold rounded-xl\"\n              >\n                <Plus className=\"w-5 h-5 mr-2\" />\n                New Project\n              </Button>\n            </div>\n\n            {/* Search */}\n            <div className=\"mb-12\">\n              <div className=\"relative max-w-xl mx-auto lg:mx-0\">\n                <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5\" />\n                <Input\n                  placeholder=\"Search projects...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-12 pr-4 py-4 text-lg bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 rounded-2xl shadow-lg focus:shadow-xl transition-all duration-300 focus:border-blue-300 dark:focus:border-blue-600\"\n                />\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Projects Grid */}\n        <div className=\"container mx-auto px-4 pb-12\">\n          {isLoading ? (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {[...Array(6)].map((_, i) => (\n                <Card key={i} className=\"animate-pulse bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 rounded-3xl p-8\">\n                  <CardHeader>\n                    <div className=\"h-8 bg-slate-200 dark:bg-slate-700 rounded-xl w-3/4 mb-4\"></div>\n                    <div className=\"h-5 bg-slate-200 dark:bg-slate-700 rounded-lg w-full\"></div>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      <div className=\"h-5 bg-slate-200 dark:bg-slate-700 rounded-lg w-1/2\"></div>\n                      <div className=\"h-5 bg-slate-200 dark:bg-slate-700 rounded-lg w-2/3\"></div>\n                      <div className=\"h-12 bg-slate-200 dark:bg-slate-700 rounded-xl w-full\"></div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          ) : filteredProjects.length === 0 ? (\n            <div className=\"text-center py-20\">\n              <div className=\"w-32 h-32 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-xl\">\n                <Code className=\"w-16 h-16 text-slate-400\" />\n              </div>\n              <h3 className=\"text-3xl font-bold mb-4 text-slate-900 dark:text-slate-100\">\n                {projects.length === 0 ? 'No projects yet' : 'No projects found'}\n              </h3>\n              <p className=\"text-xl text-slate-600 dark:text-slate-400 mb-10 max-w-2xl mx-auto leading-relaxed\">\n                {projects.length === 0\n                  ? 'Create your first project to start indexing your code and chatting with AI'\n                  : 'Try adjusting your search query to find your projects'\n                }\n              </p>\n              {projects.length === 0 && (\n                <Button\n                  onClick={() => setIsCreateDialogOpen(true)}\n                  className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-10 py-4 text-lg font-semibold rounded-xl\"\n                >\n                  <Plus className=\"w-5 h-5 mr-2\" />\n                  Create Your First Project\n                </Button>\n              )}\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {filteredProjects.map((project) => (\n                <Card\n                  key={project.id}\n                  className=\"group hover:shadow-2xl transition-all duration-500 cursor-pointer bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 hover:border-blue-300 dark:hover:border-blue-600 hover:-translate-y-2 rounded-3xl overflow-hidden\"\n                  onClick={() => window.location.href = `/projects/${project.id}`}\n                >\n                <CardHeader className=\"pb-4 p-8\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex-1\">\n                      <CardTitle className=\"text-xl font-bold group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-2\">\n                        {project.name}\n                      </CardTitle>\n                      {project.description && (\n                        <CardDescription className=\"text-slate-600 dark:text-slate-400 leading-relaxed\">\n                          {project.description}\n                        </CardDescription>\n                      )}\n                    </div>\n                    <div className=\"flex items-center gap-2 bg-slate-50 dark:bg-slate-700/50 px-3 py-1 rounded-full\">\n                      <div className={`w-2 h-2 rounded-full ${getStatusColor(project.indexingStatus)}`} />\n                      <span className=\"text-xs font-medium text-slate-600 dark:text-slate-300\">\n                        {getStatusText(project.indexingStatus)}\n                      </span>\n                    </div>\n                  </div>\n                </CardHeader>\n                <CardContent className=\"pt-0 px-8 pb-8\">\n                  <div className=\"space-y-6\">\n                    {/* Language and Framework */}\n                    <div className=\"flex gap-3 flex-wrap\">\n                      {project.language && (\n                        <Badge variant=\"secondary\" className=\"bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 dark:from-blue-900/50 dark:to-blue-800/50 dark:text-blue-300 px-3 py-1 rounded-full font-medium\">\n                          {project.language}\n                        </Badge>\n                      )}\n                      {project.framework && (\n                        <Badge variant=\"outline\" className=\"border-slate-300 dark:border-slate-600 px-3 py-1 rounded-full font-medium\">\n                          {project.framework}\n                        </Badge>\n                      )}\n                    </div>\n\n                    {/* Stats */}\n                    <div className=\"grid grid-cols-2 gap-4\">\n                      <div className=\"bg-slate-50 dark:bg-slate-700/30 rounded-2xl p-4 flex items-center gap-3\">\n                        <div className=\"w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center\">\n                          <FileText className=\"w-4 h-4 text-blue-600 dark:text-blue-400\" />\n                        </div>\n                        <div>\n                          <div className=\"text-sm font-semibold text-slate-900 dark:text-slate-100\">\n                            {project.indexedFiles}/{project.totalFiles}\n                          </div>\n                          <div className=\"text-xs text-slate-500 dark:text-slate-400\">files</div>\n                        </div>\n                      </div>\n                      <div className=\"bg-slate-50 dark:bg-slate-700/30 rounded-2xl p-4 flex items-center gap-3\">\n                        <div className=\"w-8 h-8 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center\">\n                          <Code className=\"w-4 h-4 text-purple-600 dark:text-purple-400\" />\n                        </div>\n                        <div>\n                          <div className=\"text-sm font-semibold text-slate-900 dark:text-slate-100\">\n                            {project.totalLines.toLocaleString()}\n                          </div>\n                          <div className=\"text-xs text-slate-500 dark:text-slate-400\">lines</div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Last indexed */}\n                    {project.lastIndexedAt && (\n                      <div className=\"flex items-center gap-2 text-sm text-slate-500 dark:text-slate-400 bg-slate-50 dark:bg-slate-700/30 rounded-xl px-3 py-2\">\n                        <Clock className=\"w-4 h-4\" />\n                        <span>\n                          Indexed {new Date(project.lastIndexedAt).toLocaleDateString()}\n                        </span>\n                      </div>\n                    )}\n\n                    {/* Actions */}\n                    <div className=\"flex gap-3 pt-4\">\n                      <Button\n                        size=\"sm\"\n                        className=\"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 py-3 rounded-xl font-semibold\"\n                        onClick={(e) => {\n                          e.stopPropagation()\n                          window.location.href = `/projects/${project.id}`\n                        }}\n                      >\n                        <Zap className=\"w-4 h-4 mr-2\" />\n                        Chat with AI\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        className=\"border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800 p-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-300\"\n                        onClick={(e) => e.stopPropagation()}\n                      >\n                        <ArrowRight className=\"w-4 h-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Create Project Dialog */}\n        <CreateProjectDialog\n          open={isCreateDialogOpen}\n          onOpenChange={setIsCreateDialogOpen}\n          onProjectCreated={fetchProjects}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AA2BO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ,IAAI,EAAE;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACvC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,QAAQ,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW,OACnE,QAAQ,QAAQ,EAAE,cAAc,SAAS,YAAY,WAAW;IAGlE,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0IAAA,CAAA,SAAM;;;;;oBAGN,SAAS,MAAM,KAAK,KAAK,CAAC,0BACzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACf,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAIlC,8OAAC;gDAAG,WAAU;;oDAAsM;kEAElN,8OAAC;;;;;kEACD,8OAAC;wDAAK,WAAU;kEAA0F;;;;;;;;;;;;0DAK5G,8OAAC;gDAAE,WAAU;0DAAqF;;;;;;0DAKlG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,sBAAsB;;0EAErC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,WAAU;;0EAEV,8OAAC,sMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAOzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAC1E,8OAAC;wDAAE,WAAU;kEAAqD;;;;;;;;;;;;0DAKpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAC1E,8OAAC;wDAAE,WAAU;kEAAqD;;;;;;;;;;;;0DAKpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,8OAAC;wDAAG,WAAU;kEAA4D;;;;;;kEAC1E,8OAAC;wDAAE,WAAU;kEAAqD;;;;;;;;;;;;;;;;;;kDAOtE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAOd,oBAAoB,iBACpB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwL;;;;;;0DAGtM,8OAAC;gDAAE,WAAU;0DAA6C;;;;;;;;;;;;kDAI5D,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAMrC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,8OAAC;wBAAI,WAAU;kCACZ,0BACC,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,gIAAA,CAAA,OAAI;oCAAS,WAAU;;sDACtB,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;mCATV;;;;;;;;;mCAeb,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAG,WAAU;8CACX,SAAS,MAAM,KAAK,IAAI,oBAAoB;;;;;;8CAE/C,8OAAC;oCAAE,WAAU;8CACV,SAAS,MAAM,KAAK,IACjB,+EACA;;;;;;gCAGL,SAAS,MAAM,KAAK,mBACnB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,sBAAsB;oCACrC,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;iDAMvC,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,gIAAA,CAAA,OAAI;oCAEH,WAAU;oCACV,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;;sDAEjE,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,QAAQ,IAAI;;;;;;4DAEd,QAAQ,WAAW,kBAClB,8OAAC,gIAAA,CAAA,kBAAe;gEAAC,WAAU;0EACxB,QAAQ,WAAW;;;;;;;;;;;;kEAI1B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,QAAQ,cAAc,GAAG;;;;;;0EAChF,8OAAC;gEAAK,WAAU;0EACb,cAAc,QAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;;;sDAK7C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,QAAQ,kBACf,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;0EAClC,QAAQ,QAAQ;;;;;;4DAGpB,QAAQ,SAAS,kBAChB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAChC,QAAQ,SAAS;;;;;;;;;;;;kEAMxB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;;;;;;kFAEtB,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;;oFACZ,QAAQ,YAAY;oFAAC;oFAAE,QAAQ,UAAU;;;;;;;0FAE5C,8OAAC;gFAAI,WAAU;0FAA6C;;;;;;;;;;;;;;;;;;0EAGhE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FACZ,QAAQ,UAAU,CAAC,cAAc;;;;;;0FAEpC,8OAAC;gFAAI,WAAU;0FAA6C;;;;;;;;;;;;;;;;;;;;;;;;oDAMjE,QAAQ,aAAa,kBACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;;oEAAK;oEACK,IAAI,KAAK,QAAQ,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;kEAMjE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gEAClD;;kFAEA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS,CAAC,IAAM,EAAE,eAAe;0EAEjC,cAAA,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA/FvB,QAAQ,EAAE;;;;;;;;;;;;;;;kCA2GzB,8OAAC,6JAAA,CAAA,sBAAmB;wBAClB,MAAM;wBACN,cAAc;wBACd,kBAAkB;;;;;;;;;;;;;;;;;;AAK5B", "debugId": null}}]}