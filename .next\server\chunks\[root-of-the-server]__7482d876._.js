module.exports = {

"[project]/.next-internal/server/app/api/projects/[id]/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "db": (()=>db)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const db = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = db;
}}),
"[project]/src/lib/vector.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteCodeChunks": (()=>deleteCodeChunks),
    "getIndexStats": (()=>getIndexStats),
    "searchCode": (()=>searchCode),
    "upsertCodeChunk": (()=>upsertCodeChunk),
    "vectorIndex": (()=>vectorIndex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$vector$2f$dist$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/vector/dist/nodejs.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$vector$2f$dist$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/vector/dist/nodejs.mjs [app-route] (ecmascript) <locals>");
;
let vectorIndex = null;
if (process.env.UPSTASH_VECTOR_REST_URL && process.env.UPSTASH_VECTOR_REST_TOKEN) {
    vectorIndex = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$vector$2f$dist$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Index"]({
        url: process.env.UPSTASH_VECTOR_REST_URL,
        token: process.env.UPSTASH_VECTOR_REST_TOKEN
    });
}
;
async function upsertCodeChunk(id, content, metadata) {
    if (!vectorIndex) {
        console.warn("Vector index not configured, skipping upsert");
        return {
            success: false,
            error: "Vector index not configured"
        };
    }
    try {
        await vectorIndex.upsert({
            id,
            data: content,
            metadata
        });
        return {
            success: true
        };
    } catch (error) {
        console.error("Error upserting code chunk:", error);
        return {
            success: false,
            error
        };
    }
}
async function searchCode(query, projectId, topK = 10) {
    if (!vectorIndex) {
        console.warn("Vector index not configured, skipping search");
        return {
            success: false,
            error: "Vector index not configured",
            results: []
        };
    }
    try {
        const filter = projectId ? `projectId = '${projectId}'` : undefined;
        const results = await vectorIndex.query({
            data: query,
            topK,
            includeMetadata: true,
            filter
        });
        return {
            success: true,
            results: results.map((result)=>({
                    id: result.id,
                    score: result.score,
                    content: result.metadata?.content || "",
                    metadata: result.metadata
                }))
        };
    } catch (error) {
        console.error("Error searching code:", error);
        return {
            success: false,
            error,
            results: []
        };
    }
}
async function deleteCodeChunks(ids) {
    if (!vectorIndex) {
        console.warn("Vector index not configured, skipping delete");
        return {
            success: false,
            error: "Vector index not configured"
        };
    }
    try {
        await vectorIndex.delete(ids);
        return {
            success: true
        };
    } catch (error) {
        console.error("Error deleting code chunks:", error);
        return {
            success: false,
            error
        };
    }
}
async function getIndexStats() {
    if (!vectorIndex) {
        console.warn("Vector index not configured, skipping stats");
        return {
            success: false,
            error: "Vector index not configured"
        };
    }
    try {
        const stats = await vectorIndex.info();
        return {
            success: true,
            stats
        };
    } catch (error) {
        console.error("Error getting index stats:", error);
        return {
            success: false,
            error
        };
    }
}
}}),
"[project]/src/lib/ai-providers/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OpenAIProvider": (()=>OpenAIProvider)
});
class OpenAIProvider {
    name = 'openai';
    displayName = 'OpenAI';
    config;
    constructor(config){
        this.config = {
            model: 'gpt-4',
            maxTokens: 4000,
            temperature: 0.7,
            ...config
        };
    }
    async chat(messages, functions) {
        if (!this.isConfigured()) {
            throw new Error('OpenAI API key not configured');
        }
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: this.config.model,
                messages: messages.map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                max_tokens: this.config.maxTokens,
                temperature: this.config.temperature,
                functions: functions?.map((fn)=>({
                        name: fn.name,
                        description: fn.description,
                        parameters: fn.parameters
                    }))
            })
        });
        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.statusText}`);
        }
        const data = await response.json();
        const choice = data.choices[0];
        return {
            content: choice.message.content || '',
            functionCalls: choice.message.function_call ? [
                {
                    name: choice.message.function_call.name,
                    arguments: JSON.parse(choice.message.function_call.arguments)
                }
            ] : undefined,
            usage: data.usage ? {
                promptTokens: data.usage.prompt_tokens,
                completionTokens: data.usage.completion_tokens,
                totalTokens: data.usage.total_tokens
            } : undefined
        };
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
}
}}),
"[project]/src/lib/ai-providers/anthropic.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AnthropicProvider": (()=>AnthropicProvider)
});
class AnthropicProvider {
    name = 'anthropic';
    displayName = 'Anthropic';
    config;
    constructor(config){
        this.config = {
            model: 'claude-3-sonnet-20240229',
            maxTokens: 4000,
            temperature: 0.7,
            ...config
        };
    }
    async chat(messages, functions) {
        if (!this.isConfigured()) {
            throw new Error('Anthropic API key not configured');
        }
        const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'x-api-key': this.config.apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: this.config.model,
                max_tokens: this.config.maxTokens,
                temperature: this.config.temperature,
                messages: messages.filter((msg)=>msg.role !== 'system').map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                system: messages.find((msg)=>msg.role === 'system')?.content
            })
        });
        if (!response.ok) {
            throw new Error(`Anthropic API error: ${response.statusText}`);
        }
        const data = await response.json();
        return {
            content: data.content[0]?.text || '',
            usage: data.usage ? {
                promptTokens: data.usage.input_tokens,
                completionTokens: data.usage.output_tokens,
                totalTokens: data.usage.input_tokens + data.usage.output_tokens
            } : undefined
        };
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
}
}}),
"[project]/src/lib/ai-providers/google.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GoogleProvider": (()=>GoogleProvider)
});
class GoogleProvider {
    name = 'google';
    displayName = 'Google AI';
    config;
    constructor(config){
        this.config = {
            model: 'gemini-pro',
            maxTokens: 4000,
            temperature: 0.7,
            baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
            ...config
        };
    }
    async chat(messages, functions) {
        if (!this.isConfigured()) {
            throw new Error('Google AI API key not configured');
        }
        // Convert messages to Google's format
        const contents = messages.map((msg)=>({
                role: msg.role === 'assistant' ? 'model' : 'user',
                parts: [
                    {
                        text: msg.content
                    }
                ]
            }));
        const response = await fetch(`${this.config.baseUrl}/models/${this.config.model}:generateContent?key=${this.config.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents,
                generationConfig: {
                    temperature: this.config.temperature,
                    maxOutputTokens: this.config.maxTokens
                }
            })
        });
        if (!response.ok) {
            throw new Error(`Google AI API error: ${response.statusText}`);
        }
        const data = await response.json();
        const candidate = data.candidates?.[0];
        if (!candidate) {
            throw new Error('No response from Google AI');
        }
        return {
            content: candidate.content?.parts?.[0]?.text || '',
            usage: data.usageMetadata ? {
                promptTokens: data.usageMetadata.promptTokenCount || 0,
                completionTokens: data.usageMetadata.candidatesTokenCount || 0,
                totalTokens: data.usageMetadata.totalTokenCount || 0
            } : undefined
        };
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
}
}}),
"[project]/src/lib/ai-providers/openrouter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OpenRouterProvider": (()=>OpenRouterProvider)
});
class OpenRouterProvider {
    name = 'openrouter';
    displayName = 'OpenRouter';
    config;
    constructor(config){
        this.config = {
            model: 'anthropic/claude-3-sonnet',
            maxTokens: 4000,
            temperature: 0.7,
            baseUrl: 'https://openrouter.ai/api/v1',
            ...config
        };
    }
    async chat(messages, functions) {
        if (!this.isConfigured()) {
            throw new Error('OpenRouter API key not configured');
        }
        const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:3000',
                'X-Title': 'Code Index'
            },
            body: JSON.stringify({
                model: this.config.model,
                messages: messages.map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                max_tokens: this.config.maxTokens,
                temperature: this.config.temperature,
                functions: functions?.map((fn)=>({
                        name: fn.name,
                        description: fn.description,
                        parameters: fn.parameters
                    }))
            })
        });
        if (!response.ok) {
            throw new Error(`OpenRouter API error: ${response.statusText}`);
        }
        const data = await response.json();
        const choice = data.choices[0];
        return {
            content: choice.message.content || '',
            functionCalls: choice.message.function_call ? [
                {
                    name: choice.message.function_call.name,
                    arguments: JSON.parse(choice.message.function_call.arguments)
                }
            ] : undefined,
            usage: data.usage ? {
                promptTokens: data.usage.prompt_tokens,
                completionTokens: data.usage.completion_tokens,
                totalTokens: data.usage.total_tokens
            } : undefined
        };
    }
    isConfigured() {
        return !!this.config.apiKey;
    }
}
}}),
"[project]/src/lib/ai-providers/types.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
}}),
"[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createProvider": (()=>createProvider),
    "getAvailableProviders": (()=>getAvailableProviders),
    "getConfiguredProviders": (()=>getConfiguredProviders),
    "getProviderConfig": (()=>getProviderConfig)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$anthropic$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/anthropic.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$google$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/google.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openrouter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/openrouter.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$types$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/types.ts [app-route] (ecmascript)");
;
;
;
;
;
function createProvider(name, config) {
    switch(name){
        case "openai":
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenAIProvider"](config);
        case "anthropic":
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$anthropic$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AnthropicProvider"](config);
        case "google":
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$google$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["GoogleProvider"](config);
        case "openrouter":
            return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openrouter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["OpenRouterProvider"](config);
        default:
            throw new Error(`Unknown provider: ${name}`);
    }
}
function getAvailableProviders() {
    return [
        {
            name: "openai",
            displayName: "OpenAI"
        },
        {
            name: "anthropic",
            displayName: "Anthropic"
        },
        {
            name: "google",
            displayName: "Google AI"
        },
        {
            name: "openrouter",
            displayName: "OpenRouter"
        }
    ];
}
function getConfiguredProviders() {
    const providers = getAvailableProviders();
    return providers.map((provider)=>{
        let isConfigured = false;
        try {
            const config = getProviderConfig(provider.name);
            const providerInstance = createProvider(provider.name, config);
            isConfigured = providerInstance.isConfigured();
        } catch (error) {
        // Provider not configured
        }
        return {
            ...provider,
            isConfigured
        };
    });
}
function getProviderConfig(name) {
    switch(name){
        case "openai":
            return {
                apiKey: process.env.OPENAI_API_KEY
            };
        case "anthropic":
            return {
                apiKey: process.env.ANTHROPIC_API_KEY
            };
        case "google":
            return {
                apiKey: process.env.GOOGLE_API_KEY
            };
        case "openrouter":
            return {
                apiKey: process.env.OPENROUTER_API_KEY
            };
        default:
            throw new Error(`Unknown provider: ${name}`);
    }
}
}}),
"[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$anthropic$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/anthropic.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$google$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/google.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$openrouter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/openrouter.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$types$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/types.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <locals>");
}}),
"[project]/src/app/api/projects/[id]/chat/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$vector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/vector.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/ai-providers/index.ts [app-route] (ecmascript) <locals>");
;
;
;
;
async function POST(request, { params }) {
    try {
        const { id } = await params;
        const body = await request.json();
        const { message, history = [] } = body;
        // Get project
        const project = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].project.findUnique({
            where: {
                id
            }
        });
        if (!project) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "Project not found"
            }, {
                status: 404
            });
        }
        // Search for relevant code
        const searchResults = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$vector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["searchCode"])(message, project.id, 5);
        let contextualInfo = "";
        if (searchResults.success && searchResults.results.length > 0) {
            contextualInfo = "\n\nRelevant code context:\n" + searchResults.results.map((result)=>`File: ${result.metadata.filePath}\n${result.content}`).join("\n\n");
        }
        // Get selected model (default to Qwen free model)
        const selectedModel = process.env.SELECTED_MODEL || "qwen/qwen-2.5-72b-instruct:free";
        let aiResponse;
        // Try to use OpenRouter first for the selected model
        if (process.env.OPENROUTER_API_KEY && selectedModel.includes("/")) {
            try {
                const systemMessage = `You are a helpful AI assistant that helps developers understand their codebase.
You have access to the project "${project.name}" located at "${project.path}".
${project.description ? `Project description: ${project.description}` : ""}
${project.language ? `Primary language: ${project.language}` : ""}
${project.framework ? `Framework: ${project.framework}` : ""}

When answering questions about the code, be specific and reference the actual code when possible.
Provide clear, concise explanations and include code examples when relevant.
${contextualInfo}`;
                const messages = [
                    {
                        role: "system",
                        content: systemMessage
                    },
                    ...history.map((msg)=>({
                            role: msg.role,
                            content: msg.content
                        })),
                    {
                        role: "user",
                        content: message
                    }
                ];
                const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
                    method: "POST",
                    headers: {
                        Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,
                        "Content-Type": "application/json",
                        "HTTP-Referer": process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3001",
                        "X-Title": "Code Index Chat"
                    },
                    body: JSON.stringify({
                        model: selectedModel,
                        messages,
                        temperature: 0.7,
                        max_tokens: 2000
                    })
                });
                if (response.ok) {
                    const data = await response.json();
                    aiResponse = data.choices[0]?.message?.content || "No response generated";
                } else {
                    throw new Error(`OpenRouter API error: ${response.status}`);
                }
            } catch (error) {
                console.error("OpenRouter API error:", error);
                // Fall back to other providers or mock response
                aiResponse = await tryOtherProviders(message, project, contextualInfo, history) || generateMockResponse(message, project, searchResults.results);
            }
        } else {
            // Fall back to configured providers
            aiResponse = await tryOtherProviders(message, project, contextualInfo, history) || generateMockResponse(message, project, searchResults.results);
        }
        // Save chat session and messages
        let chatSession = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].chatSession.findFirst({
            where: {
                projectId: project.id
            },
            orderBy: {
                updatedAt: "desc"
            }
        });
        if (!chatSession) {
            chatSession = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].chatSession.create({
                data: {
                    projectId: project.id,
                    title: message.slice(0, 50) + (message.length > 50 ? "..." : "")
                }
            });
        }
        // Save user message
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].chatMessage.create({
            data: {
                sessionId: chatSession.id,
                role: "user",
                content: message
            }
        });
        // Save assistant response
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].chatMessage.create({
            data: {
                sessionId: chatSession.id,
                role: "assistant",
                content: aiResponse
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            response: aiResponse
        });
    } catch (error) {
        console.error("Error in chat:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "Failed to process chat message"
        }, {
            status: 500
        });
    }
}
async function tryOtherProviders(message, project, contextualInfo, history) {
    try {
        // Get configured AI providers
        const configuredProviders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getConfiguredProviders"])();
        const availableProvider = configuredProviders.find((p)=>p.isConfigured);
        if (availableProvider) {
            const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getProviderConfig"])(availableProvider.name);
            const provider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2d$providers$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createProvider"])(availableProvider.name, config);
            const systemMessage = `You are a helpful AI assistant that helps developers understand their codebase.
You have access to the project "${project.name}" located at "${project.path}".
${project.description ? `Project description: ${project.description}` : ""}
${project.language ? `Primary language: ${project.language}` : ""}
${project.framework ? `Framework: ${project.framework}` : ""}

When answering questions about the code, be specific and reference the actual code when possible.
${contextualInfo}`;
            const messages = [
                {
                    role: "system",
                    content: systemMessage
                },
                ...history.map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                {
                    role: "user",
                    content: message
                }
            ];
            const response = await provider.chat(messages);
            return response.content;
        }
    } catch (error) {
        console.error("Other providers error:", error);
    }
    return null;
}
function generateMockResponse(message, project, searchResults) {
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes("function") || lowerMessage.includes("method")) {
        if (searchResults.length > 0) {
            return `I found some relevant functions in your ${project.name} project. Based on the code I can see, here are the functions that might be relevant to your question:\n\n${searchResults.map((r)=>`- ${r.metadata.filePath}: Contains code related to your query`).join("\n")}\n\nWould you like me to explain any specific function in detail?`;
        }
        return `I'd be happy to help you understand the functions in your ${project.name} project. However, it looks like the project hasn't been fully indexed yet. Once indexing is complete, I'll be able to provide detailed information about specific functions, their parameters, return values, and usage examples.`;
    }
    if (lowerMessage.includes("class") || lowerMessage.includes("component")) {
        return `I can help you understand the classes and components in your ${project.name} project. ${project.language === "javascript" || project.language === "typescript" ? "Since this is a JavaScript/TypeScript project, I can explain React components, ES6 classes, and their relationships." : `Since this is a ${project.language} project, I can explain the class structure and inheritance patterns.`}`;
    }
    if (lowerMessage.includes("how") && lowerMessage.includes("work")) {
        return `I can explain how different parts of your ${project.name} project work together. This includes:\n\n- Code architecture and structure\n- Function and class relationships\n- Data flow and dependencies\n- Best practices and potential improvements\n\nWhat specific part would you like me to explain?`;
    }
    if (lowerMessage.includes("bug") || lowerMessage.includes("error") || lowerMessage.includes("issue")) {
        return `I can help you debug issues in your ${project.name} project. To provide the best assistance, please:\n\n1. Describe the specific error or unexpected behavior\n2. Share the relevant code section\n3. Mention when the issue occurs\n\nI'll analyze your codebase and suggest potential solutions.`;
    }
    return `I'm here to help you understand your ${project.name} project! I can assist with:\n\n- Explaining how functions and classes work\n- Understanding code architecture\n- Finding specific code patterns\n- Debugging issues\n- Suggesting improvements\n\nWhat would you like to know about your codebase?`;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7482d876._.js.map