module.exports = {

"[project]/.next-internal/server/app/api/projects/[id]/index/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "db": (()=>db)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const db = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = db;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/vector.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "deleteCodeChunks": (()=>deleteCodeChunks),
    "getIndexStats": (()=>getIndexStats),
    "searchCode": (()=>searchCode),
    "upsertCodeChunk": (()=>upsertCodeChunk),
    "vectorIndex": (()=>vectorIndex)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$vector$2f$dist$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/vector/dist/nodejs.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$vector$2f$dist$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/vector/dist/nodejs.mjs [app-route] (ecmascript) <locals>");
;
let vectorIndex = null;
if (process.env.UPSTASH_VECTOR_REST_URL && process.env.UPSTASH_VECTOR_REST_TOKEN) {
    vectorIndex = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$vector$2f$dist$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Index"]({
        url: process.env.UPSTASH_VECTOR_REST_URL,
        token: process.env.UPSTASH_VECTOR_REST_TOKEN
    });
}
;
async function upsertCodeChunk(id, content, metadata) {
    if (!vectorIndex) {
        console.warn("Vector index not configured, skipping upsert");
        return {
            success: false,
            error: "Vector index not configured"
        };
    }
    try {
        await vectorIndex.upsert({
            id,
            data: content,
            metadata
        });
        return {
            success: true
        };
    } catch (error) {
        console.error("Error upserting code chunk:", error);
        return {
            success: false,
            error
        };
    }
}
async function searchCode(query, projectId, topK = 10) {
    if (!vectorIndex) {
        console.warn("Vector index not configured, skipping search");
        return {
            success: false,
            error: "Vector index not configured",
            results: []
        };
    }
    try {
        const filter = projectId ? `projectId = '${projectId}'` : undefined;
        const results = await vectorIndex.query({
            data: query,
            topK,
            includeMetadata: true,
            filter
        });
        return {
            success: true,
            results: results.map((result)=>({
                    id: result.id,
                    score: result.score,
                    content: result.metadata?.content || "",
                    metadata: result.metadata
                }))
        };
    } catch (error) {
        console.error("Error searching code:", error);
        return {
            success: false,
            error,
            results: []
        };
    }
}
async function deleteCodeChunks(ids) {
    if (!vectorIndex) {
        console.warn("Vector index not configured, skipping delete");
        return {
            success: false,
            error: "Vector index not configured"
        };
    }
    try {
        await vectorIndex.delete(ids);
        return {
            success: true
        };
    } catch (error) {
        console.error("Error deleting code chunks:", error);
        return {
            success: false,
            error
        };
    }
}
async function getIndexStats() {
    if (!vectorIndex) {
        console.warn("Vector index not configured, skipping stats");
        return {
            success: false,
            error: "Vector index not configured"
        };
    }
    try {
        const stats = await vectorIndex.info();
        return {
            success: true,
            stats
        };
    } catch (error) {
        console.error("Error getting index stats:", error);
        return {
            success: false,
            error
        };
    }
}
}}),
"[project]/src/lib/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "chunkCode": (()=>chunkCode),
    "cn": (()=>cn),
    "extractCodeSymbols": (()=>extractCodeSymbols),
    "formatFileSize": (()=>formatFileSize),
    "getFileExtension": (()=>getFileExtension),
    "getLanguageFromExtension": (()=>getLanguageFromExtension),
    "shouldIndexFile": (()=>shouldIndexFile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function getFileExtension(filePath) {
    return (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["extname"])(filePath).toLowerCase();
}
function getLanguageFromExtension(extension) {
    const languageMap = {
        ".js": "javascript",
        ".jsx": "javascript",
        ".ts": "typescript",
        ".tsx": "typescript",
        ".py": "python",
        ".java": "java",
        ".cpp": "cpp",
        ".c": "c",
        ".cs": "csharp",
        ".php": "php",
        ".rb": "ruby",
        ".go": "go",
        ".rs": "rust",
        ".swift": "swift",
        ".kt": "kotlin",
        ".scala": "scala",
        ".sh": "bash",
        ".sql": "sql",
        ".html": "html",
        ".css": "css",
        ".scss": "scss",
        ".sass": "sass",
        ".less": "less",
        ".vue": "vue",
        ".svelte": "svelte",
        ".md": "markdown",
        ".json": "json",
        ".xml": "xml",
        ".yaml": "yaml",
        ".yml": "yaml",
        ".toml": "toml",
        ".ini": "ini",
        ".cfg": "ini",
        ".conf": "ini"
    };
    return languageMap[extension] || "text";
}
function shouldIndexFile(filePath) {
    const extension = getFileExtension(filePath);
    const fileName = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["basename"])(filePath);
    // Skip common non-code files
    const skipExtensions = [
        ".png",
        ".jpg",
        ".jpeg",
        ".gif",
        ".svg",
        ".ico",
        ".pdf",
        ".doc",
        ".docx",
        ".xls",
        ".xlsx",
        ".zip",
        ".tar",
        ".gz",
        ".rar",
        ".mp3",
        ".mp4",
        ".avi",
        ".mov",
        ".exe",
        ".dll",
        ".so",
        ".dylib",
        ".log",
        ".tmp",
        ".cache",
        ".lock",
        ".pid"
    ];
    // Skip common directories and files
    const skipPatterns = [
        "node_modules",
        ".git",
        ".next",
        ".nuxt",
        "dist",
        "build",
        "coverage",
        ".nyc_output",
        "vendor",
        "__pycache__",
        ".pytest_cache",
        ".vscode",
        ".idea",
        "package-lock.json",
        "yarn.lock",
        "pnpm-lock.yaml",
        ".env",
        ".env.local",
        ".env.production",
        ".DS_Store",
        "Thumbs.db"
    ];
    // Check if file should be skipped
    if (skipExtensions.includes(extension)) {
        return false;
    }
    // Check if path contains skip patterns
    for (const pattern of skipPatterns){
        if (filePath.includes(pattern)) {
            return false;
        }
    }
    // Only index files with known extensions or common code files
    const codeExtensions = [
        ".js",
        ".jsx",
        ".ts",
        ".tsx",
        ".vue",
        ".svelte",
        ".py",
        ".java",
        ".cpp",
        ".c",
        ".cs",
        ".php",
        ".rb",
        ".go",
        ".rs",
        ".swift",
        ".kt",
        ".scala",
        ".sh",
        ".sql",
        ".html",
        ".css",
        ".scss",
        ".sass",
        ".less",
        ".md",
        ".json",
        ".xml",
        ".yaml",
        ".yml",
        ".toml",
        ".ini",
        ".cfg",
        ".conf",
        ".txt",
        ".readme"
    ];
    return codeExtensions.includes(extension) || fileName.toLowerCase().includes("readme");
}
function chunkCode(content, maxChunkSize = 1000) {
    const lines = content.split("\n");
    const chunks = [];
    let currentChunk = "";
    for (const line of lines){
        if (currentChunk.length + line.length + 1 > maxChunkSize && currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
            currentChunk = line;
        } else {
            currentChunk += (currentChunk ? "\n" : "") + line;
        }
    }
    if (currentChunk.trim()) {
        chunks.push(currentChunk.trim());
    }
    return chunks.filter((chunk)=>chunk.length > 0);
}
function extractCodeSymbols(content, language) {
    const symbols = [];
    try {
        switch(language){
            case "javascript":
            case "typescript":
                // Extract function names, class names, etc.
                const jsPatterns = [
                    /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
                    /class\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
                    /const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g,
                    /let\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g,
                    /var\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g,
                    /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*function/g,
                    /([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=>\s*/g
                ];
                for (const pattern of jsPatterns){
                    let match;
                    while((match = pattern.exec(content)) !== null){
                        symbols.push(match[1]);
                    }
                }
                break;
            case "python":
                // Extract Python function and class names
                const pyPatterns = [
                    /def\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
                    /class\s+([a-zA-Z_][a-zA-Z0-9_]*)/g
                ];
                for (const pattern of pyPatterns){
                    let match;
                    while((match = pattern.exec(content)) !== null){
                        symbols.push(match[1]);
                    }
                }
                break;
            case "java":
                // Extract Java method and class names
                const javaPatterns = [
                    /class\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
                    /interface\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,
                    /public\s+\w+\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
                    /private\s+\w+\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
                    /protected\s+\w+\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g
                ];
                for (const pattern of javaPatterns){
                    let match;
                    while((match = pattern.exec(content)) !== null){
                        symbols.push(match[1]);
                    }
                }
                break;
        }
    } catch (error) {
        console.error("Error extracting symbols:", error);
    }
    return [
        ...new Set(symbols)
    ]; // Remove duplicates
}
function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = [
        "Bytes",
        "KB",
        "MB",
        "GB"
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/lib/indexing.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "indexProject": (()=>indexProject)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$vector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/vector.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
;
;
;
;
;
async function indexProject(projectId, onProgress, incrementalOnly = false) {
    try {
        const project = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].project.findUnique({
            where: {
                id: projectId
            }
        });
        if (!project) {
            throw new Error("Project not found");
        }
        // Update project status
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].project.update({
            where: {
                id: projectId
            },
            data: {
                indexingStatus: "indexing"
            }
        });
        const progressUpdate = {
            totalFiles: 0,
            processedFiles: 0,
            currentFile: "Scanning files...",
            status: "scanning"
        };
        onProgress?.(progressUpdate);
        // Update progress in global store for real-time tracking
        if (typeof globalThis !== "undefined") {
            globalThis.indexingProgress = globalThis.indexingProgress || new Map();
            globalThis.indexingProgress.set(projectId, {
                ...progressUpdate,
                startTime: new Date()
            });
        }
        let filesToProcess = [];
        if (incrementalOnly) {
            // Only process files that are not indexed or have been updated
            const unindexedFiles = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].projectFile.findMany({
                where: {
                    projectId,
                    isIndexed: false
                }
            });
            // Convert database files to the expected format
            filesToProcess = unindexedFiles.map((file)=>({
                    path: `${project.path}/${file.path}`,
                    relativePath: file.path,
                    name: file.name,
                    size: file.size,
                    lines: file.lines
                }));
        } else {
            // Scan directory for all files
            const files = await scanDirectory(project.path);
            filesToProcess = files.filter((file)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["shouldIndexFile"])(file.path));
        }
        const indexableFiles = filesToProcess;
        const indexingUpdate = {
            totalFiles: indexableFiles.length,
            processedFiles: 0,
            currentFile: "Starting indexing...",
            status: "indexing"
        };
        onProgress?.(indexingUpdate);
        // Update global progress
        if (typeof globalThis !== "undefined" && globalThis.indexingProgress) {
            const existing = globalThis.indexingProgress.get(projectId);
            globalThis.indexingProgress.set(projectId, {
                ...existing,
                ...indexingUpdate
            });
        }
        let processedCount = 0;
        let totalLines = 0;
        for (const file of indexableFiles){
            try {
                const fileUpdate = {
                    totalFiles: indexableFiles.length,
                    processedFiles: processedCount,
                    currentFile: file.relativePath,
                    status: "indexing"
                };
                onProgress?.(fileUpdate);
                // Update global progress
                if (typeof globalThis !== "undefined" && globalThis.indexingProgress) {
                    const existing = globalThis.indexingProgress.get(projectId);
                    globalThis.indexingProgress.set(projectId, {
                        ...existing,
                        ...fileUpdate
                    });
                }
                await indexFile(projectId, file);
                totalLines += file.lines;
                processedCount++;
            } catch (error) {
                console.error(`Error indexing file ${file.path}:`, error);
            // Continue with other files
            }
        }
        // Update project statistics
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].project.update({
            where: {
                id: projectId
            },
            data: {
                indexingStatus: "completed",
                isIndexed: true,
                totalFiles: indexableFiles.length,
                indexedFiles: processedCount,
                totalLines,
                lastIndexedAt: new Date()
            }
        });
        const completedUpdate = {
            totalFiles: indexableFiles.length,
            processedFiles: processedCount,
            currentFile: "Completed!",
            status: "completed"
        };
        onProgress?.(completedUpdate);
        // Update global progress
        if (typeof globalThis !== "undefined" && globalThis.indexingProgress) {
            const existing = globalThis.indexingProgress.get(projectId);
            globalThis.indexingProgress.set(projectId, {
                ...existing,
                ...completedUpdate
            });
            // Clear progress after 30 seconds
            setTimeout(()=>{
                if (globalThis.indexingProgress) {
                    globalThis.indexingProgress.delete(projectId);
                }
            }, 30000);
        }
        return {
            success: true,
            processedFiles: processedCount,
            totalLines
        };
    } catch (error) {
        console.error("Error indexing project:", error);
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].project.update({
            where: {
                id: projectId
            },
            data: {
                indexingStatus: "failed"
            }
        });
        const errorUpdate = {
            totalFiles: 0,
            processedFiles: 0,
            currentFile: "",
            status: "failed",
            error: error instanceof Error ? error.message : "Unknown error"
        };
        onProgress?.(errorUpdate);
        // Update global progress
        if (typeof globalThis !== "undefined" && globalThis.indexingProgress) {
            const existing = globalThis.indexingProgress.get(projectId);
            globalThis.indexingProgress.set(projectId, {
                ...existing,
                ...errorUpdate
            });
        }
        return {
            success: false,
            error
        };
    }
}
async function scanDirectory(dirPath) {
    const files = [];
    async function scanRecursive(currentPath, relativePath = "") {
        try {
            const entries = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].readdir(currentPath, {
                withFileTypes: true
            });
            for (const entry of entries){
                const fullPath = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(currentPath, entry.name);
                const relPath = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(relativePath, entry.name);
                if (entry.isDirectory()) {
                    // Skip common directories that shouldn't be indexed
                    const skipDirs = [
                        "node_modules",
                        ".git",
                        "dist",
                        "build",
                        ".next",
                        "coverage",
                        "vendor",
                        "__pycache__",
                        "target",
                        "bin",
                        "obj"
                    ];
                    if (!skipDirs.includes(entry.name)) {
                        await scanRecursive(fullPath, relPath);
                    }
                } else if (entry.isFile()) {
                    try {
                        const stats = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].stat(fullPath);
                        const content = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].readFile(fullPath, "utf-8");
                        const lines = content.split("\n").length;
                        files.push({
                            path: fullPath,
                            relativePath: relPath,
                            name: entry.name,
                            size: stats.size,
                            lines
                        });
                    } catch (error) {
                        // Skip files that can't be read
                        console.warn(`Skipping file ${fullPath}:`, error);
                    }
                }
            }
        } catch (error) {
            console.warn(`Error scanning directory ${currentPath}:`, error);
        }
    }
    await scanRecursive(dirPath);
    return files;
}
async function indexFile(projectId, file) {
    const content = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].readFile(file.path, "utf-8");
    const hash = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHash("md5").update(content).digest("hex");
    const extension = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getFileExtension"])(file.name);
    const language = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLanguageFromExtension"])(extension);
    // Check if file already exists and hasn't changed
    const existingFile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].projectFile.findFirst({
        where: {
            projectId,
            path: file.relativePath
        }
    });
    if (existingFile && existingFile.hash === hash && existingFile.isIndexed) {
        // File hasn't changed, skip indexing
        return;
    }
    // Create or update file record
    const projectFile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].projectFile.upsert({
        where: {
            projectId_path: {
                projectId,
                path: file.relativePath
            }
        },
        update: {
            name: file.name,
            extension,
            size: file.size,
            lines: file.lines,
            content: file.size < 100000 ? content : null,
            hash,
            isIndexed: false
        },
        create: {
            projectId,
            path: file.relativePath,
            name: file.name,
            extension,
            size: file.size,
            lines: file.lines,
            content: file.size < 100000 ? content : null,
            hash,
            isIndexed: false
        }
    });
    // Delete existing chunks
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].fileChunk.deleteMany({
        where: {
            fileId: projectFile.id
        }
    });
    // Chunk the file content
    const chunks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["chunkCode"])(content, 1000);
    const symbols = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractCodeSymbols"])(content, language);
    // Index each chunk
    for(let i = 0; i < chunks.length; i++){
        const chunk = chunks[i];
        const chunkId = `${projectFile.id}-chunk-${i}`;
        const metadata = {
            projectId,
            fileId: projectFile.id,
            filePath: file.relativePath,
            fileName: file.name,
            language,
            startLine: chunk.startLine,
            endLine: chunk.endLine,
            chunkType: "general",
            symbols: symbols.length > 0 ? symbols : undefined
        };
        // Upsert to vector database
        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$vector$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["upsertCodeChunk"])(chunkId, chunk.content, metadata);
        if (result.success) {
            // Save chunk to database
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].fileChunk.create({
                data: {
                    fileId: projectFile.id,
                    content: chunk.content,
                    startLine: chunk.startLine,
                    endLine: chunk.endLine,
                    vectorId: chunkId
                }
            });
        }
    }
    // Mark file as indexed
    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].projectFile.update({
        where: {
            id: projectFile.id
        },
        data: {
            isIndexed: true,
            lastIndexedAt: new Date()
        }
    });
}
}}),
"[project]/src/app/api/projects/[id]/index/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$indexing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/indexing.ts [app-route] (ecmascript)");
;
;
;
async function POST(request, { params }) {
    try {
        const { id } = await params;
        const project = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].project.findUnique({
            where: {
                id
            }
        });
        if (!project) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "Project not found"
            }, {
                status: 404
            });
        }
        // Check if already indexing
        if (project.indexingStatus === "indexing") {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "Project is already being indexed"
            }, {
                status: 400
            });
        }
        // Parse request body to check for incremental flag
        const body = await request.json().catch(()=>({}));
        const incremental = body.incremental === true;
        // Start indexing in background
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$indexing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["indexProject"])(id, undefined, incremental).catch((error)=>{
            console.error("Background indexing failed:", error);
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: incremental ? "Incremental indexing started" : "Full indexing started",
            status: "indexing",
            incremental
        });
    } catch (error) {
        console.error("Error starting indexing:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "Failed to start indexing"
        }, {
            status: 500
        });
    }
}
async function GET(request, { params }) {
    try {
        const { id } = await params;
        const project = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["db"].project.findUnique({
            where: {
                id
            },
            include: {
                files: {
                    select: {
                        id: true,
                        path: true,
                        name: true,
                        extension: true,
                        size: true,
                        lines: true,
                        isIndexed: true,
                        lastIndexedAt: true
                    },
                    orderBy: {
                        path: "asc"
                    }
                }
            }
        });
        if (!project) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: "Project not found"
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            project: {
                id: project.id,
                name: project.name,
                indexingStatus: project.indexingStatus,
                totalFiles: project.totalFiles,
                indexedFiles: project.indexedFiles,
                totalLines: project.totalLines,
                lastIndexedAt: project.lastIndexedAt
            },
            files: project.files
        });
    } catch (error) {
        console.error("Error fetching indexing status:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: "Failed to fetch indexing status"
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a06f327a._.js.map