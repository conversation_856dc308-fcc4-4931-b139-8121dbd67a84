{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/app/api/settings/models/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\ninterface OpenRouterModel {\n  id: string\n  name: string\n  description?: string\n  pricing: {\n    prompt: string\n    completion: string\n  }\n  context_length: number\n  architecture: {\n    modality: string\n    tokenizer: string\n    instruct_type?: string\n  }\n  top_provider: {\n    context_length: number\n    max_completion_tokens?: number\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Fetch models from OpenRouter\n    const response = await fetch('https://openrouter.ai/api/v1/models', {\n      headers: {\n        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY || ''}`,\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error('Failed to fetch models from OpenRouter')\n    }\n\n    const data = await response.json()\n    const models: OpenRouterModel[] = data.data || []\n\n    // Filter and format models\n    const formattedModels = models\n      .filter(model => {\n        // Filter for popular and free models\n        return model.id.includes('free') || \n               model.id.includes('qwen') ||\n               model.id.includes('llama') ||\n               model.id.includes('mistral') ||\n               model.id.includes('claude') ||\n               model.id.includes('gpt')\n      })\n      .map(model => ({\n        id: model.id,\n        name: model.name,\n        description: model.description || '',\n        pricing: {\n          prompt: model.pricing.prompt,\n          completion: model.pricing.completion,\n        },\n        contextLength: model.context_length,\n        isFree: model.pricing.prompt === '0' && model.pricing.completion === '0',\n        provider: 'openrouter',\n      }))\n      .sort((a, b) => {\n        // Sort free models first, then by name\n        if (a.isFree && !b.isFree) return -1\n        if (!a.isFree && b.isFree) return 1\n        return a.name.localeCompare(b.name)\n      })\n\n    // Add some popular models from other providers\n    const otherModels = [\n      {\n        id: 'gpt-4o',\n        name: 'GPT-4o',\n        description: 'Latest GPT-4 model with improved capabilities',\n        pricing: { prompt: '0.005', completion: '0.015' },\n        contextLength: 128000,\n        isFree: false,\n        provider: 'openai',\n      },\n      {\n        id: 'gpt-4o-mini',\n        name: 'GPT-4o Mini',\n        description: 'Faster and cheaper GPT-4 variant',\n        pricing: { prompt: '0.00015', completion: '0.0006' },\n        contextLength: 128000,\n        isFree: false,\n        provider: 'openai',\n      },\n      {\n        id: 'claude-3-5-sonnet-20241022',\n        name: 'Claude 3.5 Sonnet',\n        description: 'Latest Claude model with excellent reasoning',\n        pricing: { prompt: '0.003', completion: '0.015' },\n        contextLength: 200000,\n        isFree: false,\n        provider: 'anthropic',\n      },\n      {\n        id: 'gemini-1.5-pro',\n        name: 'Gemini 1.5 Pro',\n        description: 'Google\\'s most capable model',\n        pricing: { prompt: '0.00125', completion: '0.005' },\n        contextLength: 2000000,\n        isFree: false,\n        provider: 'google',\n      },\n    ]\n\n    const allModels = [...formattedModels, ...otherModels]\n\n    return NextResponse.json({\n      models: allModels,\n      totalCount: allModels.length,\n    })\n\n  } catch (error) {\n    console.error('Error fetching models:', error)\n    \n    // Return fallback models if API fails\n    const fallbackModels = [\n      {\n        id: 'qwen/qwen-2.5-72b-instruct:free',\n        name: 'Qwen 2.5 72B Instruct (Free)',\n        description: 'High-quality free model from Alibaba',\n        pricing: { prompt: '0', completion: '0' },\n        contextLength: 32768,\n        isFree: true,\n        provider: 'openrouter',\n      },\n      {\n        id: 'meta-llama/llama-3.1-8b-instruct:free',\n        name: 'Llama 3.1 8B Instruct (Free)',\n        description: 'Meta\\'s open-source model',\n        pricing: { prompt: '0', completion: '0' },\n        contextLength: 131072,\n        isFree: true,\n        provider: 'openrouter',\n      },\n      {\n        id: 'gpt-4o-mini',\n        name: 'GPT-4o Mini',\n        description: 'OpenAI\\'s efficient model',\n        pricing: { prompt: '0.00015', completion: '0.0006' },\n        contextLength: 128000,\n        isFree: false,\n        provider: 'openai',\n      },\n    ]\n\n    return NextResponse.json({\n      models: fallbackModels,\n      totalCount: fallbackModels.length,\n      error: 'Failed to fetch latest models, showing fallback list',\n    })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { selectedModel, provider } = await request.json()\n\n    // Here you would save the selected model to your database or settings\n    // For now, we'll just return success\n    \n    return NextResponse.json({\n      success: true,\n      message: 'Model selection saved successfully',\n      selectedModel,\n      provider,\n    })\n\n  } catch (error) {\n    console.error('Error saving model selection:', error)\n    return NextResponse.json(\n      { error: 'Failed to save model selection' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAsBO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,+BAA+B;QAC/B,MAAM,WAAW,MAAM,MAAM,uCAAuC;YAClE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,kBAAkB,IAAI,IAAI;gBACjE,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,SAA4B,KAAK,IAAI,IAAI,EAAE;QAEjD,2BAA2B;QAC3B,MAAM,kBAAkB,OACrB,MAAM,CAAC,CAAA;YACN,qCAAqC;YACrC,OAAO,MAAM,EAAE,CAAC,QAAQ,CAAC,WAClB,MAAM,EAAE,CAAC,QAAQ,CAAC,WAClB,MAAM,EAAE,CAAC,QAAQ,CAAC,YAClB,MAAM,EAAE,CAAC,QAAQ,CAAC,cAClB,MAAM,EAAE,CAAC,QAAQ,CAAC,aAClB,MAAM,EAAE,CAAC,QAAQ,CAAC;QAC3B,GACC,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,IAAI,MAAM,EAAE;gBACZ,MAAM,MAAM,IAAI;gBAChB,aAAa,MAAM,WAAW,IAAI;gBAClC,SAAS;oBACP,QAAQ,MAAM,OAAO,CAAC,MAAM;oBAC5B,YAAY,MAAM,OAAO,CAAC,UAAU;gBACtC;gBACA,eAAe,MAAM,cAAc;gBACnC,QAAQ,MAAM,OAAO,CAAC,MAAM,KAAK,OAAO,MAAM,OAAO,CAAC,UAAU,KAAK;gBACrE,UAAU;YACZ,CAAC,GACA,IAAI,CAAC,CAAC,GAAG;YACR,uCAAuC;YACvC,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC;YACnC,IAAI,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,EAAE,OAAO;YAClC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI;QACpC;QAEF,+CAA+C;QAC/C,MAAM,cAAc;YAClB;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;oBAAE,QAAQ;oBAAS,YAAY;gBAAQ;gBAChD,eAAe;gBACf,QAAQ;gBACR,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;oBAAE,QAAQ;oBAAW,YAAY;gBAAS;gBACnD,eAAe;gBACf,QAAQ;gBACR,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;oBAAE,QAAQ;oBAAS,YAAY;gBAAQ;gBAChD,eAAe;gBACf,QAAQ;gBACR,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;oBAAE,QAAQ;oBAAW,YAAY;gBAAQ;gBAClD,eAAe;gBACf,QAAQ;gBACR,UAAU;YACZ;SACD;QAED,MAAM,YAAY;eAAI;eAAoB;SAAY;QAEtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,YAAY,UAAU,MAAM;QAC9B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QAExC,sCAAsC;QACtC,MAAM,iBAAiB;YACrB;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;oBAAE,QAAQ;oBAAK,YAAY;gBAAI;gBACxC,eAAe;gBACf,QAAQ;gBACR,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;oBAAE,QAAQ;oBAAK,YAAY;gBAAI;gBACxC,eAAe;gBACf,QAAQ;gBACR,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;oBAAE,QAAQ;oBAAW,YAAY;gBAAS;gBACnD,eAAe;gBACf,QAAQ;gBACR,UAAU;YACZ;SACD;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,YAAY,eAAe,MAAM;YACjC,OAAO;QACT;IACF;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEtD,sEAAsE;QACtE,qCAAqC;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}