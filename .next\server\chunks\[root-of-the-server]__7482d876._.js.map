{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/vector.ts"], "sourcesContent": ["import { Index } from \"@upstash/vector\";\n\nlet vectorIndex: Index | null = null;\n\nif (\n  process.env.UPSTASH_VECTOR_REST_URL &&\n  process.env.UPSTASH_VECTOR_REST_TOKEN\n) {\n  vectorIndex = new Index({\n    url: process.env.UPSTASH_VECTOR_REST_URL,\n    token: process.env.UPSTASH_VECTOR_REST_TOKEN,\n  });\n}\n\nexport { vectorIndex };\n\nexport interface CodeMetadata {\n  projectId: string;\n  fileId: string;\n  filePath: string;\n  fileName: string;\n  language?: string;\n  startLine: number;\n  endLine: number;\n  chunkType:\n    | \"function\"\n    | \"class\"\n    | \"interface\"\n    | \"variable\"\n    | \"comment\"\n    | \"general\";\n  symbols?: string[]; // Function names, class names, etc.\n}\n\nexport async function upsertCodeChunk(\n  id: string,\n  content: string,\n  metadata: CodeMetadata\n) {\n  if (!vectorIndex) {\n    console.warn(\"Vector index not configured, skipping upsert\");\n    return { success: false, error: \"Vector index not configured\" };\n  }\n\n  try {\n    await vectorIndex.upsert({\n      id,\n      data: content, // Let Up<PERSON>sh handle embedding generation\n      metadata,\n    });\n    return { success: true };\n  } catch (error) {\n    console.error(\"Error upserting code chunk:\", error);\n    return { success: false, error };\n  }\n}\n\nexport async function searchCode(\n  query: string,\n  projectId?: string,\n  topK: number = 10\n) {\n  if (!vectorIndex) {\n    console.warn(\"Vector index not configured, skipping search\");\n    return {\n      success: false,\n      error: \"Vector index not configured\",\n      results: [],\n    };\n  }\n\n  try {\n    const filter = projectId ? `projectId = '${projectId}'` : undefined;\n\n    const results = await vectorIndex.query({\n      data: query,\n      topK,\n      includeMetadata: true,\n      filter,\n    });\n\n    return {\n      success: true,\n      results: results.map((result) => ({\n        id: result.id,\n        score: result.score,\n        content: result.metadata?.content || \"\",\n        metadata: result.metadata as CodeMetadata,\n      })),\n    };\n  } catch (error) {\n    console.error(\"Error searching code:\", error);\n    return { success: false, error, results: [] };\n  }\n}\n\nexport async function deleteCodeChunks(ids: string[]) {\n  if (!vectorIndex) {\n    console.warn(\"Vector index not configured, skipping delete\");\n    return { success: false, error: \"Vector index not configured\" };\n  }\n\n  try {\n    await vectorIndex.delete(ids);\n    return { success: true };\n  } catch (error) {\n    console.error(\"Error deleting code chunks:\", error);\n    return { success: false, error };\n  }\n}\n\nexport async function getIndexStats() {\n  if (!vectorIndex) {\n    console.warn(\"Vector index not configured, skipping stats\");\n    return { success: false, error: \"Vector index not configured\" };\n  }\n\n  try {\n    const stats = await vectorIndex.info();\n    return { success: true, stats };\n  } catch (error) {\n    console.error(\"Error getting index stats:\", error);\n    return { success: false, error };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAEA,IAAI,cAA4B;AAEhC,IACE,QAAQ,GAAG,CAAC,uBAAuB,IACnC,QAAQ,GAAG,CAAC,yBAAyB,EACrC;IACA,cAAc,IAAI,wKAAA,CAAA,QAAK,CAAC;QACtB,KAAK,QAAQ,GAAG,CAAC,uBAAuB;QACxC,OAAO,QAAQ,GAAG,CAAC,yBAAyB;IAC9C;AACF;;AAsBO,eAAe,gBACpB,EAAU,EACV,OAAe,EACf,QAAsB;IAEtB,IAAI,CAAC,aAAa;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;QAA8B;IAChE;IAEA,IAAI;QACF,MAAM,YAAY,MAAM,CAAC;YACvB;YACA,MAAM;YACN;QACF;QACA,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,eAAe,WACpB,KAAa,EACb,SAAkB,EAClB,OAAe,EAAE;IAEjB,IAAI,CAAC,aAAa;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,SAAS;YACT,OAAO;YACP,SAAS,EAAE;QACb;IACF;IAEA,IAAI;QACF,MAAM,SAAS,YAAY,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,GAAG;QAE1D,MAAM,UAAU,MAAM,YAAY,KAAK,CAAC;YACtC,MAAM;YACN;YACA,iBAAiB;YACjB;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,QAAQ,GAAG,CAAC,CAAC,SAAW,CAAC;oBAChC,IAAI,OAAO,EAAE;oBACb,OAAO,OAAO,KAAK;oBACnB,SAAS,OAAO,QAAQ,EAAE,WAAW;oBACrC,UAAU,OAAO,QAAQ;gBAC3B,CAAC;QACH;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;YAAE,SAAS;YAAO;YAAO,SAAS,EAAE;QAAC;IAC9C;AACF;AAEO,eAAe,iBAAiB,GAAa;IAClD,IAAI,CAAC,aAAa;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;QAA8B;IAChE;IAEA,IAAI;QACF,MAAM,YAAY,MAAM,CAAC;QACzB,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,eAAe;IACpB,IAAI,CAAC,aAAa;QAChB,QAAQ,IAAI,CAAC;QACb,OAAO;YAAE,SAAS;YAAO,OAAO;QAA8B;IAChE;IAEA,IAAI;QACF,MAAM,QAAQ,MAAM,YAAY,IAAI;QACpC,OAAO;YAAE,SAAS;YAAM;QAAM;IAChC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/openai.ts"], "sourcesContent": ["import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from './types'\n\nexport class OpenAIProvider implements AIProvider {\n  name = 'openai'\n  displayName = 'OpenAI'\n  \n  private config: ProviderConfig\n\n  constructor(config: ProviderConfig) {\n    this.config = {\n      model: 'gpt-4',\n      maxTokens: 4000,\n      temperature: 0.7,\n      ...config,\n    }\n  }\n\n  async chat(messages: ChatMessage[], functions?: Function[]): Promise<ChatResponse> {\n    if (!this.isConfigured()) {\n      throw new Error('OpenAI API key not configured')\n    }\n\n    const response = await fetch('https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${this.config.apiKey}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        model: this.config.model,\n        messages: messages.map(msg => ({\n          role: msg.role,\n          content: msg.content,\n        })),\n        max_tokens: this.config.maxTokens,\n        temperature: this.config.temperature,\n        functions: functions?.map(fn => ({\n          name: fn.name,\n          description: fn.description,\n          parameters: fn.parameters,\n        })),\n      }),\n    })\n\n    if (!response.ok) {\n      throw new Error(`OpenAI API error: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    const choice = data.choices[0]\n\n    return {\n      content: choice.message.content || '',\n      functionCalls: choice.message.function_call ? [{\n        name: choice.message.function_call.name,\n        arguments: JSON.parse(choice.message.function_call.arguments),\n      }] : undefined,\n      usage: data.usage ? {\n        promptTokens: data.usage.prompt_tokens,\n        completionTokens: data.usage.completion_tokens,\n        totalTokens: data.usage.total_tokens,\n      } : undefined,\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.config.apiKey\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,SAAQ;IACf,cAAc,SAAQ;IAEd,OAAsB;IAE9B,YAAY,MAAsB,CAAE;QAClC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO;YACP,WAAW;YACX,aAAa;YACb,GAAG,MAAM;QACX;IACF;IAEA,MAAM,KAAK,QAAuB,EAAE,SAAsB,EAAyB;QACjF,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,8CAA8C;YACzE,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,UAAU,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC7B,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;gBACjC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;gBACpC,WAAW,WAAW,IAAI,CAAA,KAAM,CAAC;wBAC/B,MAAM,GAAG,IAAI;wBACb,aAAa,GAAG,WAAW;wBAC3B,YAAY,GAAG,UAAU;oBAC3B,CAAC;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,UAAU,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;QAE9B,OAAO;YACL,SAAS,OAAO,OAAO,CAAC,OAAO,IAAI;YACnC,eAAe,OAAO,OAAO,CAAC,aAAa,GAAG;gBAAC;oBAC7C,MAAM,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI;oBACvC,WAAW,KAAK,KAAK,CAAC,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS;gBAC9D;aAAE,GAAG;YACL,OAAO,KAAK,KAAK,GAAG;gBAClB,cAAc,KAAK,KAAK,CAAC,aAAa;gBACtC,kBAAkB,KAAK,KAAK,CAAC,iBAAiB;gBAC9C,aAAa,KAAK,KAAK,CAAC,YAAY;YACtC,IAAI;QACN;IACF;IAEA,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/anthropic.ts"], "sourcesContent": ["import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from './types'\n\nexport class AnthropicProvider implements AIProvider {\n  name = 'anthropic'\n  displayName = 'Anthropic'\n  \n  private config: ProviderConfig\n\n  constructor(config: ProviderConfig) {\n    this.config = {\n      model: 'claude-3-sonnet-20240229',\n      maxTokens: 4000,\n      temperature: 0.7,\n      ...config,\n    }\n  }\n\n  async chat(messages: ChatMessage[], functions?: Function[]): Promise<ChatResponse> {\n    if (!this.isConfigured()) {\n      throw new Error('Anthropic API key not configured')\n    }\n\n    const response = await fetch('https://api.anthropic.com/v1/messages', {\n      method: 'POST',\n      headers: {\n        'x-api-key': this.config.apiKey!,\n        'Content-Type': 'application/json',\n        'anthropic-version': '2023-06-01',\n      },\n      body: JSON.stringify({\n        model: this.config.model,\n        max_tokens: this.config.maxTokens,\n        temperature: this.config.temperature,\n        messages: messages.filter(msg => msg.role !== 'system').map(msg => ({\n          role: msg.role,\n          content: msg.content,\n        })),\n        system: messages.find(msg => msg.role === 'system')?.content,\n      }),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Anthropic API error: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    return {\n      content: data.content[0]?.text || '',\n      usage: data.usage ? {\n        promptTokens: data.usage.input_tokens,\n        completionTokens: data.usage.output_tokens,\n        totalTokens: data.usage.input_tokens + data.usage.output_tokens,\n      } : undefined,\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.config.apiKey\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,YAAW;IAClB,cAAc,YAAW;IAEjB,OAAsB;IAE9B,YAAY,MAAsB,CAAE;QAClC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO;YACP,WAAW;YACX,aAAa;YACb,GAAG,MAAM;QACX;IACF;IAEA,MAAM,KAAK,QAAuB,EAAE,SAAsB,EAAyB;QACjF,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,yCAAyC;YACpE,QAAQ;YACR,SAAS;gBACP,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC/B,gBAAgB;gBAChB,qBAAqB;YACvB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;gBACjC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;gBACpC,UAAU,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,UAAU,GAAG,CAAC,CAAA,MAAO,CAAC;wBAClE,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD,QAAQ,SAAS,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,WAAW;YACvD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;QAC/D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL,SAAS,KAAK,OAAO,CAAC,EAAE,EAAE,QAAQ;YAClC,OAAO,KAAK,KAAK,GAAG;gBAClB,cAAc,KAAK,KAAK,CAAC,YAAY;gBACrC,kBAAkB,KAAK,KAAK,CAAC,aAAa;gBAC1C,aAAa,KAAK,KAAK,CAAC,YAAY,GAAG,KAAK,KAAK,CAAC,aAAa;YACjE,IAAI;QACN;IACF;IAEA,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/google.ts"], "sourcesContent": ["import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from './types'\n\nexport class GoogleProvider implements AIProvider {\n  name = 'google'\n  displayName = 'Google AI'\n  \n  private config: ProviderConfig\n\n  constructor(config: ProviderConfig) {\n    this.config = {\n      model: 'gemini-pro',\n      maxTokens: 4000,\n      temperature: 0.7,\n      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',\n      ...config,\n    }\n  }\n\n  async chat(messages: ChatMessage[], functions?: Function[]): Promise<ChatResponse> {\n    if (!this.isConfigured()) {\n      throw new Error('Google AI API key not configured')\n    }\n\n    // Convert messages to Google's format\n    const contents = messages.map(msg => ({\n      role: msg.role === 'assistant' ? 'model' : 'user',\n      parts: [{ text: msg.content }],\n    }))\n\n    const response = await fetch(\n      `${this.config.baseUrl}/models/${this.config.model}:generateContent?key=${this.config.apiKey}`,\n      {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents,\n          generationConfig: {\n            temperature: this.config.temperature,\n            maxOutputTokens: this.config.maxTokens,\n          },\n        }),\n      }\n    )\n\n    if (!response.ok) {\n      throw new Error(`Google AI API error: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    const candidate = data.candidates?.[0]\n\n    if (!candidate) {\n      throw new Error('No response from Google AI')\n    }\n\n    return {\n      content: candidate.content?.parts?.[0]?.text || '',\n      usage: data.usageMetadata ? {\n        promptTokens: data.usageMetadata.promptTokenCount || 0,\n        completionTokens: data.usageMetadata.candidatesTokenCount || 0,\n        totalTokens: data.usageMetadata.totalTokenCount || 0,\n      } : undefined,\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.config.apiKey\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,SAAQ;IACf,cAAc,YAAW;IAEjB,OAAsB;IAE9B,YAAY,MAAsB,CAAE;QAClC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO;YACP,WAAW;YACX,aAAa;YACb,SAAS;YACT,GAAG,MAAM;QACX;IACF;IAEA,MAAM,KAAK,QAAuB,EAAE,SAAsB,EAAyB;QACjF,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,WAAW,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,MAAM,IAAI,IAAI,KAAK,cAAc,UAAU;gBAC3C,OAAO;oBAAC;wBAAE,MAAM,IAAI,OAAO;oBAAC;iBAAE;YAChC,CAAC;QAED,MAAM,WAAW,MAAM,MACrB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAC9F;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,kBAAkB;oBAChB,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;oBACpC,iBAAiB,IAAI,CAAC,MAAM,CAAC,SAAS;gBACxC;YACF;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;QAC/D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,YAAY,KAAK,UAAU,EAAE,CAAC,EAAE;QAEtC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;YACL,SAAS,UAAU,OAAO,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ;YAChD,OAAO,KAAK,aAAa,GAAG;gBAC1B,cAAc,KAAK,aAAa,CAAC,gBAAgB,IAAI;gBACrD,kBAAkB,KAAK,aAAa,CAAC,oBAAoB,IAAI;gBAC7D,aAAa,KAAK,aAAa,CAAC,eAAe,IAAI;YACrD,IAAI;QACN;IACF;IAEA,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/openrouter.ts"], "sourcesContent": ["import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from './types'\n\nexport class OpenRouterProvider implements AIProvider {\n  name = 'openrouter'\n  displayName = 'OpenRouter'\n  \n  private config: ProviderConfig\n\n  constructor(config: ProviderConfig) {\n    this.config = {\n      model: 'anthropic/claude-3-sonnet',\n      maxTokens: 4000,\n      temperature: 0.7,\n      baseUrl: 'https://openrouter.ai/api/v1',\n      ...config,\n    }\n  }\n\n  async chat(messages: ChatMessage[], functions?: Function[]): Promise<ChatResponse> {\n    if (!this.isConfigured()) {\n      throw new Error('OpenRouter API key not configured')\n    }\n\n    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${this.config.apiKey}`,\n        'Content-Type': 'application/json',\n        'HTTP-Referer': 'http://localhost:3000',\n        'X-Title': 'Code Index',\n      },\n      body: JSON.stringify({\n        model: this.config.model,\n        messages: messages.map(msg => ({\n          role: msg.role,\n          content: msg.content,\n        })),\n        max_tokens: this.config.maxTokens,\n        temperature: this.config.temperature,\n        functions: functions?.map(fn => ({\n          name: fn.name,\n          description: fn.description,\n          parameters: fn.parameters,\n        })),\n      }),\n    })\n\n    if (!response.ok) {\n      throw new Error(`OpenRouter API error: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    const choice = data.choices[0]\n\n    return {\n      content: choice.message.content || '',\n      functionCalls: choice.message.function_call ? [{\n        name: choice.message.function_call.name,\n        arguments: JSON.parse(choice.message.function_call.arguments),\n      }] : undefined,\n      usage: data.usage ? {\n        promptTokens: data.usage.prompt_tokens,\n        completionTokens: data.usage.completion_tokens,\n        totalTokens: data.usage.total_tokens,\n      } : undefined,\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.config.apiKey\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,aAAY;IACnB,cAAc,aAAY;IAElB,OAAsB;IAE9B,YAAY,MAAsB,CAAE;QAClC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO;YACP,WAAW;YACX,aAAa;YACb,SAAS;YACT,GAAG,MAAM;QACX;IACF;IAEA,MAAM,KAAK,QAAuB,EAAE,SAAsB,EAAyB;QACjF,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACtE,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,gBAAgB;gBAChB,gBAAgB;gBAChB,WAAW;YACb;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,UAAU,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC7B,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;gBACjC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;gBACpC,WAAW,WAAW,IAAI,CAAA,KAAM,CAAC;wBAC/B,MAAM,GAAG,IAAI;wBACb,aAAa,GAAG,WAAW;wBAC3B,YAAY,GAAG,UAAU;oBAC3B,CAAC;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAChE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;QAE9B,OAAO;YACL,SAAS,OAAO,OAAO,CAAC,OAAO,IAAI;YACnC,eAAe,OAAO,OAAO,CAAC,aAAa,GAAG;gBAAC;oBAC7C,MAAM,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI;oBACvC,WAAW,KAAK,KAAK,CAAC,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS;gBAC9D;aAAE,GAAG;YACL,OAAO,KAAK,KAAK,GAAG;gBAClB,cAAc,KAAK,KAAK,CAAC,aAAa;gBACtC,kBAAkB,KAAK,KAAK,CAAC,iBAAiB;gBAC9C,aAAa,KAAK,KAAK,CAAC,YAAY;YACtC,IAAI;QACN;IACF;IAEA,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/index.ts"], "sourcesContent": ["import { OpenAIProvider } from \"./openai\";\nimport { AnthropicProvider } from \"./anthropic\";\nimport { GoogleProvider } from \"./google\";\nimport { OpenRouterProvider } from \"./openrouter\";\nimport { AIProvider, ProviderConfig } from \"./types\";\n\nexport * from \"./types\";\n\nexport function createProvider(\n  name: string,\n  config: ProviderConfig\n): AIProvider {\n  switch (name) {\n    case \"openai\":\n      return new OpenAIProvider(config);\n    case \"anthropic\":\n      return new AnthropicProvider(config);\n    case \"google\":\n      return new GoogleProvider(config);\n    case \"openrouter\":\n      return new OpenRouterProvider(config);\n    default:\n      throw new Error(`Unknown provider: ${name}`);\n  }\n}\n\nexport function getAvailableProviders(): Array<{\n  name: string;\n  displayName: string;\n}> {\n  return [\n    { name: \"openai\", displayName: \"OpenAI\" },\n    { name: \"anthropic\", displayName: \"Anthropic\" },\n    { name: \"google\", displayName: \"Google AI\" },\n    { name: \"openrouter\", displayName: \"OpenRouter\" },\n  ];\n}\n\nexport function getConfiguredProviders(): Array<{\n  name: string;\n  displayName: string;\n  isConfigured: boolean;\n}> {\n  const providers = getAvailableProviders();\n\n  return providers.map((provider) => {\n    let isConfigured = false;\n\n    try {\n      const config = getProviderConfig(provider.name);\n      const providerInstance = createProvider(provider.name, config);\n      isConfigured = providerInstance.isConfigured();\n    } catch (error) {\n      // Provider not configured\n    }\n\n    return {\n      ...provider,\n      isConfigured,\n    };\n  });\n}\n\nexport function getProviderConfig(name: string): ProviderConfig {\n  switch (name) {\n    case \"openai\":\n      return {\n        apiKey: process.env.OPENAI_API_KEY,\n      };\n    case \"anthropic\":\n      return {\n        apiKey: process.env.ANTHROPIC_API_KEY,\n      };\n    case \"google\":\n      return {\n        apiKey: process.env.GOOGLE_API_KEY,\n      };\n    case \"openrouter\":\n      return {\n        apiKey: process.env.OPENROUTER_API_KEY,\n      };\n    default:\n      throw new Error(`Unknown provider: ${name}`);\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAGA;;;;;;AAEO,SAAS,eACd,IAAY,EACZ,MAAsB;IAEtB,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,yIAAA,CAAA,iBAAc,CAAC;QAC5B,KAAK;YACH,OAAO,IAAI,4IAAA,CAAA,oBAAiB,CAAC;QAC/B,KAAK;YACH,OAAO,IAAI,yIAAA,CAAA,iBAAc,CAAC;QAC5B,KAAK;YACH,OAAO,IAAI,6IAAA,CAAA,qBAAkB,CAAC;QAChC;YACE,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM;IAC/C;AACF;AAEO,SAAS;IAId,OAAO;QACL;YAAE,MAAM;YAAU,aAAa;QAAS;QACxC;YAAE,MAAM;YAAa,aAAa;QAAY;QAC9C;YAAE,MAAM;YAAU,aAAa;QAAY;QAC3C;YAAE,MAAM;YAAc,aAAa;QAAa;KACjD;AACH;AAEO,SAAS;IAKd,MAAM,YAAY;IAElB,OAAO,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,eAAe;QAEnB,IAAI;YACF,MAAM,SAAS,kBAAkB,SAAS,IAAI;YAC9C,MAAM,mBAAmB,eAAe,SAAS,IAAI,EAAE;YACvD,eAAe,iBAAiB,YAAY;QAC9C,EAAE,OAAO,OAAO;QACd,0BAA0B;QAC5B;QAEA,OAAO;YACL,GAAG,QAAQ;YACX;QACF;IACF;AACF;AAEO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,QAAQ,QAAQ,GAAG,CAAC,cAAc;YACpC;QACF,KAAK;YACH,OAAO;gBACL,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;YACvC;QACF,KAAK;YACH,OAAO;gBACL,QAAQ,QAAQ,GAAG,CAAC,cAAc;YACpC;QACF,KAAK;YACH,OAAO;gBACL,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;YACxC;QACF;YACE,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/app/api/projects/%5Bid%5D/chat/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { db } from \"@/lib/db\";\nimport { searchCode } from \"@/lib/vector\";\nimport {\n  createProvider,\n  getConfiguredProviders,\n  getProviderConfig,\n} from \"@/lib/ai-providers\";\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params;\n    const body = await request.json();\n    const { message, history = [] } = body;\n\n    // Get project\n    const project = await db.project.findUnique({\n      where: { id },\n    });\n\n    if (!project) {\n      return NextResponse.json({ error: \"Project not found\" }, { status: 404 });\n    }\n\n    // Search for relevant code\n    const searchResults = await searchCode(message, project.id, 5);\n\n    let contextualInfo = \"\";\n    if (searchResults.success && searchResults.results.length > 0) {\n      contextualInfo =\n        \"\\n\\nRelevant code context:\\n\" +\n        searchResults.results\n          .map(\n            (result) => `File: ${result.metadata.filePath}\\n${result.content}`\n          )\n          .join(\"\\n\\n\");\n    }\n\n    // Get selected model (default to Qwen free model)\n    const selectedModel =\n      process.env.SELECTED_MODEL || \"qwen/qwen-2.5-72b-instruct:free\";\n\n    let aiResponse: string;\n\n    // Try to use OpenRouter first for the selected model\n    if (process.env.OPENROUTER_API_KEY && selectedModel.includes(\"/\")) {\n      try {\n        const systemMessage = `You are a helpful AI assistant that helps developers understand their codebase.\nYou have access to the project \"${project.name}\" located at \"${project.path}\".\n${project.description ? `Project description: ${project.description}` : \"\"}\n${project.language ? `Primary language: ${project.language}` : \"\"}\n${project.framework ? `Framework: ${project.framework}` : \"\"}\n\nWhen answering questions about the code, be specific and reference the actual code when possible.\nProvide clear, concise explanations and include code examples when relevant.\n${contextualInfo}`;\n\n        const messages = [\n          { role: \"system\", content: systemMessage },\n          ...history.map((msg: any) => ({\n            role: msg.role,\n            content: msg.content,\n          })),\n          { role: \"user\", content: message },\n        ];\n\n        const response = await fetch(\n          \"https://openrouter.ai/api/v1/chat/completions\",\n          {\n            method: \"POST\",\n            headers: {\n              Authorization: `Bearer ${process.env.OPENROUTER_API_KEY}`,\n              \"Content-Type\": \"application/json\",\n              \"HTTP-Referer\":\n                process.env.NEXT_PUBLIC_APP_URL || \"http://localhost:3001\",\n              \"X-Title\": \"Code Index Chat\",\n            },\n            body: JSON.stringify({\n              model: selectedModel,\n              messages,\n              temperature: 0.7,\n              max_tokens: 2000,\n            }),\n          }\n        );\n\n        if (response.ok) {\n          const data = await response.json();\n          aiResponse =\n            data.choices[0]?.message?.content || \"No response generated\";\n        } else {\n          throw new Error(`OpenRouter API error: ${response.status}`);\n        }\n      } catch (error) {\n        console.error(\"OpenRouter API error:\", error);\n        // Fall back to other providers or mock response\n        aiResponse =\n          (await tryOtherProviders(\n            message,\n            project,\n            contextualInfo,\n            history\n          )) || generateMockResponse(message, project, searchResults.results);\n      }\n    } else {\n      // Fall back to configured providers\n      aiResponse =\n        (await tryOtherProviders(message, project, contextualInfo, history)) ||\n        generateMockResponse(message, project, searchResults.results);\n    }\n\n    // Save chat session and messages\n    let chatSession = await db.chatSession.findFirst({\n      where: { projectId: project.id },\n      orderBy: { updatedAt: \"desc\" },\n    });\n\n    if (!chatSession) {\n      chatSession = await db.chatSession.create({\n        data: {\n          projectId: project.id,\n          title: message.slice(0, 50) + (message.length > 50 ? \"...\" : \"\"),\n        },\n      });\n    }\n\n    // Save user message\n    await db.chatMessage.create({\n      data: {\n        sessionId: chatSession.id,\n        role: \"user\",\n        content: message,\n      },\n    });\n\n    // Save assistant response\n    await db.chatMessage.create({\n      data: {\n        sessionId: chatSession.id,\n        role: \"assistant\",\n        content: aiResponse,\n      },\n    });\n\n    return NextResponse.json({ response: aiResponse });\n  } catch (error) {\n    console.error(\"Error in chat:\", error);\n    return NextResponse.json(\n      { error: \"Failed to process chat message\" },\n      { status: 500 }\n    );\n  }\n}\n\nasync function tryOtherProviders(\n  message: string,\n  project: any,\n  contextualInfo: string,\n  history: any[]\n): Promise<string | null> {\n  try {\n    // Get configured AI providers\n    const configuredProviders = getConfiguredProviders();\n    const availableProvider = configuredProviders.find((p) => p.isConfigured);\n\n    if (availableProvider) {\n      const config = getProviderConfig(availableProvider.name);\n      const provider = createProvider(availableProvider.name, config);\n\n      const systemMessage = `You are a helpful AI assistant that helps developers understand their codebase.\nYou have access to the project \"${project.name}\" located at \"${project.path}\".\n${project.description ? `Project description: ${project.description}` : \"\"}\n${project.language ? `Primary language: ${project.language}` : \"\"}\n${project.framework ? `Framework: ${project.framework}` : \"\"}\n\nWhen answering questions about the code, be specific and reference the actual code when possible.\n${contextualInfo}`;\n\n      const messages = [\n        { role: \"system\" as const, content: systemMessage },\n        ...history.map((msg: any) => ({\n          role: msg.role as \"user\" | \"assistant\",\n          content: msg.content,\n        })),\n        { role: \"user\" as const, content: message },\n      ];\n\n      const response = await provider.chat(messages);\n      return response.content;\n    }\n  } catch (error) {\n    console.error(\"Other providers error:\", error);\n  }\n\n  return null;\n}\n\nfunction generateMockResponse(\n  message: string,\n  project: any,\n  searchResults: any[]\n): string {\n  const lowerMessage = message.toLowerCase();\n\n  if (lowerMessage.includes(\"function\") || lowerMessage.includes(\"method\")) {\n    if (searchResults.length > 0) {\n      return `I found some relevant functions in your ${\n        project.name\n      } project. Based on the code I can see, here are the functions that might be relevant to your question:\\n\\n${searchResults\n        .map(\n          (r) => `- ${r.metadata.filePath}: Contains code related to your query`\n        )\n        .join(\n          \"\\n\"\n        )}\\n\\nWould you like me to explain any specific function in detail?`;\n    }\n    return `I'd be happy to help you understand the functions in your ${project.name} project. However, it looks like the project hasn't been fully indexed yet. Once indexing is complete, I'll be able to provide detailed information about specific functions, their parameters, return values, and usage examples.`;\n  }\n\n  if (lowerMessage.includes(\"class\") || lowerMessage.includes(\"component\")) {\n    return `I can help you understand the classes and components in your ${\n      project.name\n    } project. ${\n      project.language === \"javascript\" || project.language === \"typescript\"\n        ? \"Since this is a JavaScript/TypeScript project, I can explain React components, ES6 classes, and their relationships.\"\n        : `Since this is a ${project.language} project, I can explain the class structure and inheritance patterns.`\n    }`;\n  }\n\n  if (lowerMessage.includes(\"how\") && lowerMessage.includes(\"work\")) {\n    return `I can explain how different parts of your ${project.name} project work together. This includes:\\n\\n- Code architecture and structure\\n- Function and class relationships\\n- Data flow and dependencies\\n- Best practices and potential improvements\\n\\nWhat specific part would you like me to explain?`;\n  }\n\n  if (\n    lowerMessage.includes(\"bug\") ||\n    lowerMessage.includes(\"error\") ||\n    lowerMessage.includes(\"issue\")\n  ) {\n    return `I can help you debug issues in your ${project.name} project. To provide the best assistance, please:\\n\\n1. Describe the specific error or unexpected behavior\\n2. Share the relevant code section\\n3. Mention when the issue occurs\\n\\nI'll analyze your codebase and suggest potential solutions.`;\n  }\n\n  return `I'm here to help you understand your ${project.name} project! I can assist with:\\n\\n- Explaining how functions and classes work\\n- Understanding code architecture\\n- Finding specific code patterns\\n- Debugging issues\\n- Suggesting improvements\\n\\nWhat would you like to know about your codebase?`;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;;;;;AAMO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,EAAE,GAAG;QAElC,cAAc;QACd,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,2BAA2B;QAC3B,MAAM,gBAAgB,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,QAAQ,EAAE,EAAE;QAE5D,IAAI,iBAAiB;QACrB,IAAI,cAAc,OAAO,IAAI,cAAc,OAAO,CAAC,MAAM,GAAG,GAAG;YAC7D,iBACE,iCACA,cAAc,OAAO,CAClB,GAAG,CACF,CAAC,SAAW,CAAC,MAAM,EAAE,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,OAAO,EAAE,EAEnE,IAAI,CAAC;QACZ;QAEA,kDAAkD;QAClD,MAAM,gBACJ,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEhC,IAAI;QAEJ,qDAAqD;QACrD,IAAI,QAAQ,GAAG,CAAC,kBAAkB,IAAI,cAAc,QAAQ,CAAC,MAAM;YACjE,IAAI;gBACF,MAAM,gBAAgB,CAAC;gCACC,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,CAAC;AAC5E,EAAE,QAAQ,WAAW,GAAG,CAAC,qBAAqB,EAAE,QAAQ,WAAW,EAAE,GAAG,GAAG;AAC3E,EAAE,QAAQ,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ,EAAE,GAAG,GAAG;AAClE,EAAE,QAAQ,SAAS,GAAG,CAAC,WAAW,EAAE,QAAQ,SAAS,EAAE,GAAG,GAAG;;;;AAI7D,EAAE,gBAAgB;gBAEV,MAAM,WAAW;oBACf;wBAAE,MAAM;wBAAU,SAAS;oBAAc;uBACtC,QAAQ,GAAG,CAAC,CAAC,MAAa,CAAC;4BAC5B,MAAM,IAAI,IAAI;4BACd,SAAS,IAAI,OAAO;wBACtB,CAAC;oBACD;wBAAE,MAAM;wBAAQ,SAAS;oBAAQ;iBAClC;gBAED,MAAM,WAAW,MAAM,MACrB,iDACA;oBACE,QAAQ;oBACR,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE;wBACzD,gBAAgB;wBAChB,gBACE,QAAQ,GAAG,CAAC,mBAAmB,IAAI;wBACrC,WAAW;oBACb;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,OAAO;wBACP;wBACA,aAAa;wBACb,YAAY;oBACd;gBACF;gBAGF,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,aACE,KAAK,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;gBACzC,OAAO;oBACL,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,MAAM,EAAE;gBAC5D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,gDAAgD;gBAChD,aACE,AAAC,MAAM,kBACL,SACA,SACA,gBACA,YACI,qBAAqB,SAAS,SAAS,cAAc,OAAO;YACtE;QACF,OAAO;YACL,oCAAoC;YACpC,aACE,AAAC,MAAM,kBAAkB,SAAS,SAAS,gBAAgB,YAC3D,qBAAqB,SAAS,SAAS,cAAc,OAAO;QAChE;QAEA,iCAAiC;QACjC,IAAI,cAAc,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,SAAS,CAAC;YAC/C,OAAO;gBAAE,WAAW,QAAQ,EAAE;YAAC;YAC/B,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,IAAI,CAAC,aAAa;YAChB,cAAc,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,MAAM,CAAC;gBACxC,MAAM;oBACJ,WAAW,QAAQ,EAAE;oBACrB,OAAO,QAAQ,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,QAAQ,EAAE;gBACjE;YACF;QACF;QAEA,oBAAoB;QACpB,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,MAAM,CAAC;YAC1B,MAAM;gBACJ,WAAW,YAAY,EAAE;gBACzB,MAAM;gBACN,SAAS;YACX;QACF;QAEA,0BAA0B;QAC1B,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,MAAM,CAAC;YAC1B,MAAM;gBACJ,WAAW,YAAY,EAAE;gBACzB,MAAM;gBACN,SAAS;YACX;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,UAAU;QAAW;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,kBACb,OAAe,EACf,OAAY,EACZ,cAAsB,EACtB,OAAc;IAEd,IAAI;QACF,8BAA8B;QAC9B,MAAM,sBAAsB,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD;QACjD,MAAM,oBAAoB,oBAAoB,IAAI,CAAC,CAAC,IAAM,EAAE,YAAY;QAExE,IAAI,mBAAmB;YACrB,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,oBAAiB,AAAD,EAAE,kBAAkB,IAAI;YACvD,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,IAAI,EAAE;YAExD,MAAM,gBAAgB,CAAC;gCACG,EAAE,QAAQ,IAAI,CAAC,cAAc,EAAE,QAAQ,IAAI,CAAC;AAC5E,EAAE,QAAQ,WAAW,GAAG,CAAC,qBAAqB,EAAE,QAAQ,WAAW,EAAE,GAAG,GAAG;AAC3E,EAAE,QAAQ,QAAQ,GAAG,CAAC,kBAAkB,EAAE,QAAQ,QAAQ,EAAE,GAAG,GAAG;AAClE,EAAE,QAAQ,SAAS,GAAG,CAAC,WAAW,EAAE,QAAQ,SAAS,EAAE,GAAG,GAAG;;;AAG7D,EAAE,gBAAgB;YAEZ,MAAM,WAAW;gBACf;oBAAE,MAAM;oBAAmB,SAAS;gBAAc;mBAC/C,QAAQ,GAAG,CAAC,CAAC,MAAa,CAAC;wBAC5B,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD;oBAAE,MAAM;oBAAiB,SAAS;gBAAQ;aAC3C;YAED,MAAM,WAAW,MAAM,SAAS,IAAI,CAAC;YACrC,OAAO,SAAS,OAAO;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;IAC1C;IAEA,OAAO;AACT;AAEA,SAAS,qBACP,OAAe,EACf,OAAY,EACZ,aAAoB;IAEpB,MAAM,eAAe,QAAQ,WAAW;IAExC,IAAI,aAAa,QAAQ,CAAC,eAAe,aAAa,QAAQ,CAAC,WAAW;QACxE,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,OAAO,CAAC,wCAAwC,EAC9C,QAAQ,IAAI,CACb,0GAA0G,EAAE,cAC1G,GAAG,CACF,CAAC,IAAM,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,QAAQ,CAAC,qCAAqC,CAAC,EAEvE,IAAI,CACH,MACA,iEAAiE,CAAC;QACxE;QACA,OAAO,CAAC,0DAA0D,EAAE,QAAQ,IAAI,CAAC,kOAAkO,CAAC;IACtT;IAEA,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,cAAc;QACxE,OAAO,CAAC,6DAA6D,EACnE,QAAQ,IAAI,CACb,UAAU,EACT,QAAQ,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ,KAAK,eACtD,yHACA,CAAC,gBAAgB,EAAE,QAAQ,QAAQ,CAAC,qEAAqE,CAAC,EAC9G;IACJ;IAEA,IAAI,aAAa,QAAQ,CAAC,UAAU,aAAa,QAAQ,CAAC,SAAS;QACjE,OAAO,CAAC,0CAA0C,EAAE,QAAQ,IAAI,CAAC,8OAA8O,CAAC;IAClT;IAEA,IACE,aAAa,QAAQ,CAAC,UACtB,aAAa,QAAQ,CAAC,YACtB,aAAa,QAAQ,CAAC,UACtB;QACA,OAAO,CAAC,oCAAoC,EAAE,QAAQ,IAAI,CAAC,+OAA+O,CAAC;IAC7S;IAEA,OAAO,CAAC,qCAAqC,EAAE,QAAQ,IAAI,CAAC,mPAAmP,CAAC;AAClT", "debugId": null}}]}