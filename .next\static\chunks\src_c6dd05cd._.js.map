{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport * as path from \"path\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// File extension utilities\nexport function getFileExtension(filePath: string): string {\n  return path.extname(filePath).toLowerCase();\n}\n\nexport function getLanguageFromExtension(extension: string): string {\n  const languageMap: Record<string, string> = {\n    \".js\": \"javascript\",\n    \".jsx\": \"javascript\",\n    \".ts\": \"typescript\",\n    \".tsx\": \"typescript\",\n    \".py\": \"python\",\n    \".java\": \"java\",\n    \".cpp\": \"cpp\",\n    \".c\": \"c\",\n    \".cs\": \"csharp\",\n    \".php\": \"php\",\n    \".rb\": \"ruby\",\n    \".go\": \"go\",\n    \".rs\": \"rust\",\n    \".swift\": \"swift\",\n    \".kt\": \"kotlin\",\n    \".scala\": \"scala\",\n    \".sh\": \"bash\",\n    \".sql\": \"sql\",\n    \".html\": \"html\",\n    \".css\": \"css\",\n    \".scss\": \"scss\",\n    \".sass\": \"sass\",\n    \".less\": \"less\",\n    \".vue\": \"vue\",\n    \".svelte\": \"svelte\",\n    \".md\": \"markdown\",\n    \".json\": \"json\",\n    \".xml\": \"xml\",\n    \".yaml\": \"yaml\",\n    \".yml\": \"yaml\",\n    \".toml\": \"toml\",\n    \".ini\": \"ini\",\n    \".cfg\": \"ini\",\n    \".conf\": \"ini\",\n  };\n\n  return languageMap[extension] || \"text\";\n}\n\n// File filtering\nconst SKIP_EXTENSIONS = [\n  // Images\n  \".png\",\n  \".jpg\",\n  \".jpeg\",\n  \".gif\",\n  \".svg\",\n  \".ico\",\n  \".webp\",\n  \".bmp\",\n  \".tiff\",\n  // Documents\n  \".pdf\",\n  \".doc\",\n  \".docx\",\n  \".xls\",\n  \".xlsx\",\n  \".ppt\",\n  \".pptx\",\n  // Archives\n  \".zip\",\n  \".tar\",\n  \".gz\",\n  \".rar\",\n  \".7z\",\n  \".bz2\",\n  \".xz\",\n  // Media\n  \".mp3\",\n  \".mp4\",\n  \".avi\",\n  \".mov\",\n  \".wmv\",\n  \".flv\",\n  \".mkv\",\n  \".wav\",\n  \".ogg\",\n  // Executables\n  \".exe\",\n  \".dll\",\n  \".so\",\n  \".dylib\",\n  \".app\",\n  \".deb\",\n  \".rpm\",\n  \".msi\",\n  // Temporary/Cache\n  \".log\",\n  \".tmp\",\n  \".cache\",\n  \".lock\",\n  \".pid\",\n  \".swp\",\n  \".swo\",\n  \".bak\",\n  // Font files\n  \".ttf\",\n  \".otf\",\n  \".woff\",\n  \".woff2\",\n  \".eot\",\n  // Database files\n  \".db\",\n  \".sqlite\",\n  \".sqlite3\",\n];\n\nconst SKIP_PATTERNS = [\n  // Dependencies\n  \"node_modules\",\n  \"vendor\",\n  \"__pycache__\",\n  \".pytest_cache\",\n  // Version control\n  \".git\",\n  \".svn\",\n  \".hg\",\n  // Build outputs\n  \".next\",\n  \".nuxt\",\n  \"dist\",\n  \"build\",\n  \"out\",\n  \"target\",\n  \"bin\",\n  \"obj\",\n  // IDE/Editor\n  \".vscode\",\n  \".idea\",\n  \".vs\",\n  \".sublime-project\",\n  \".sublime-workspace\",\n  // Testing/Coverage\n  \"coverage\",\n  \".nyc_output\",\n  \".coverage\",\n  \"htmlcov\",\n  // Lock files\n  \"package-lock.json\",\n  \"yarn.lock\",\n  \"pnpm-lock.yaml\",\n  \"composer.lock\",\n  \"Pipfile.lock\",\n  // Environment files\n  \".env\",\n  \".env.local\",\n  \".env.development\",\n  \".env.production\",\n  \".env.staging\",\n  // OS files\n  \".DS_Store\",\n  \"Thumbs.db\",\n  \"desktop.ini\",\n  // Logs\n  \"logs\",\n  \"*.log\",\n  // Cache directories\n  \".cache\",\n  \".parcel-cache\",\n  \".webpack\",\n  \".rollup.cache\",\n];\n\nconst SKIP_FILENAMES = [\n  // Lock files (exact matches)\n  \"package-lock.json\",\n  \"yarn.lock\",\n  \"pnpm-lock.yaml\",\n  \"composer.lock\",\n  \"Pipfile.lock\",\n  // Environment files\n  \".env\",\n  \".env.local\",\n  \".env.development\",\n  \".env.production\",\n  \".env.staging\",\n  // OS files\n  \".DS_Store\",\n  \"Thumbs.db\",\n  \"desktop.ini\",\n  // IDE files\n  \".gitignore\",\n  \".gitattributes\",\n  \".editorconfig\",\n];\n\nconst CODE_EXTENSIONS = [\n  // Web technologies\n  \".js\",\n  \".jsx\",\n  \".ts\",\n  \".tsx\",\n  \".vue\",\n  \".svelte\",\n  \".astro\",\n  \".html\",\n  \".htm\",\n  \".css\",\n  \".scss\",\n  \".sass\",\n  \".less\",\n  \".stylus\",\n  // Programming languages\n  \".py\",\n  \".java\",\n  \".cpp\",\n  \".c\",\n  \".cs\",\n  \".php\",\n  \".rb\",\n  \".go\",\n  \".rs\",\n  \".swift\",\n  \".kt\",\n  \".scala\",\n  \".clj\",\n  \".cljs\",\n  \".hs\",\n  \".elm\",\n  \".dart\",\n  \".lua\",\n  \".perl\",\n  \".r\",\n  \".matlab\",\n  \".julia\",\n  \".f90\",\n  \".f95\",\n  // Shell/Scripts\n  \".sh\",\n  \".bash\",\n  \".zsh\",\n  \".fish\",\n  \".ps1\",\n  \".bat\",\n  \".cmd\",\n  // Data/Config\n  \".json\",\n  \".xml\",\n  \".yaml\",\n  \".yml\",\n  \".toml\",\n  \".ini\",\n  \".cfg\",\n  \".conf\",\n  \".properties\",\n  \".env.example\",\n  \".env.template\",\n  // Database\n  \".sql\",\n  \".graphql\",\n  \".gql\",\n  // Documentation\n  \".md\",\n  \".mdx\",\n  \".txt\",\n  \".rst\",\n  \".adoc\",\n  // Other\n  \".dockerfile\",\n  \".gitignore\",\n  \".gitattributes\",\n  \".editorconfig\",\n];\n\nfunction shouldSkipPath(filePath: string): boolean {\n  const normalizedPath = filePath.replace(/\\\\/g, \"/\");\n  const pathParts = normalizedPath.split(\"/\");\n\n  // Check if any part of the path matches skip patterns\n  for (const part of pathParts) {\n    for (const pattern of SKIP_PATTERNS) {\n      if (part === pattern || part.includes(pattern)) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n}\n\nexport function shouldUploadFile(filePath: string): boolean {\n  const extension = getFileExtension(filePath);\n  const fileName = path.basename(filePath);\n\n  // Skip files with unwanted extensions\n  if (SKIP_EXTENSIONS.includes(extension)) {\n    return false;\n  }\n\n  // Skip specific filenames\n  if (SKIP_FILENAMES.includes(fileName)) {\n    return false;\n  }\n\n  // Skip paths containing unwanted patterns\n  if (shouldSkipPath(filePath)) {\n    return false;\n  }\n\n  // Only allow files with code extensions or common project files\n  return (\n    CODE_EXTENSIONS.includes(extension) ||\n    fileName.toLowerCase().includes(\"readme\") ||\n    fileName.toLowerCase().includes(\"license\") ||\n    fileName.toLowerCase().includes(\"changelog\") ||\n    fileName.toLowerCase().includes(\"makefile\") ||\n    fileName === \"Dockerfile\" ||\n    fileName === \"docker-compose.yml\" ||\n    fileName === \"docker-compose.yaml\"\n  );\n}\n\nexport function shouldIndexFile(filePath: string): boolean {\n  // Use the same logic as shouldUploadFile for consistency\n  return shouldUploadFile(filePath);\n}\n\n// Code chunking\nexport function chunkCode(\n  content: string,\n  maxChunkSize: number = 1000\n): string[] {\n  const lines = content.split(\"\\n\");\n  const chunks: string[] = [];\n  let currentChunk = \"\";\n\n  for (const line of lines) {\n    if (\n      currentChunk.length + line.length + 1 > maxChunkSize &&\n      currentChunk.length > 0\n    ) {\n      chunks.push(currentChunk.trim());\n      currentChunk = line;\n    } else {\n      currentChunk += (currentChunk ? \"\\n\" : \"\") + line;\n    }\n  }\n\n  if (currentChunk.trim()) {\n    chunks.push(currentChunk.trim());\n  }\n\n  return chunks.filter((chunk) => chunk.length > 0);\n}\n\n// Code symbol extraction\nexport function extractCodeSymbols(\n  content: string,\n  language: string\n): string[] {\n  const symbols: string[] = [];\n\n  try {\n    switch (language) {\n      case \"javascript\":\n      case \"typescript\":\n        // Extract function names, class names, etc.\n        const jsPatterns = [\n          /function\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n          /class\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,\n          /const\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /let\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /var\\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=/g,\n          /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*:\\s*function/g,\n          /([a-zA-Z_$][a-zA-Z0-9_$]*)\\s*=>\\s*/g,\n        ];\n\n        for (const pattern of jsPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n\n      case \"python\":\n        // Extract Python function and class names\n        const pyPatterns = [\n          /def\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n        ];\n\n        for (const pattern of pyPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n\n      case \"java\":\n        // Extract Java method and class names\n        const javaPatterns = [\n          /class\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /interface\\s+([a-zA-Z_][a-zA-Z0-9_]*)/g,\n          /public\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n          /private\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n          /protected\\s+\\w+\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(/g,\n        ];\n\n        for (const pattern of javaPatterns) {\n          let match;\n          while ((match = pattern.exec(content)) !== null) {\n            symbols.push(match[1]);\n          }\n        }\n        break;\n    }\n  } catch (error) {\n    console.error(\"Error extracting symbols:\", error);\n  }\n\n  return [...new Set(symbols)]; // Remove duplicates\n}\n\n// File size formatting\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return \"0 Bytes\";\n\n  const k = 1024;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,CAAA,GAAA,0KAAA,CAAA,UAAY,AAAD,EAAE,UAAU,WAAW;AAC3C;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,cAAsC;QAC1C,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,UAAU;QACV,OAAO;QACP,UAAU;QACV,OAAO;QACP,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW;QACX,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;IACX;IAEA,OAAO,WAAW,CAAC,UAAU,IAAI;AACnC;AAEA,iBAAiB;AACjB,MAAM,kBAAkB;IACtB,SAAS;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,YAAY;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,kBAAkB;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,iBAAiB;IACjB;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IACpB,eAAe;IACf;IACA;IACA;IACA;IACA,kBAAkB;IAClB;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,mBAAmB;IACnB;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,OAAO;IACP;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB,6BAA6B;IAC7B;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,YAAY;IACZ;IACA;IACA;CACD;AAED,MAAM,kBAAkB;IACtB,mBAAmB;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,wBAAwB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,cAAc;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,WAAW;IACX;IACA;IACA;IACA,gBAAgB;IAChB;IACA;IACA;IACA;IACA;IACA,QAAQ;IACR;IACA;IACA;IACA;CACD;AAED,SAAS,eAAe,QAAgB;IACtC,MAAM,iBAAiB,SAAS,OAAO,CAAC,OAAO;IAC/C,MAAM,YAAY,eAAe,KAAK,CAAC;IAEvC,sDAAsD;IACtD,KAAK,MAAM,QAAQ,UAAW;QAC5B,KAAK,MAAM,WAAW,cAAe;YACnC,IAAI,SAAS,WAAW,KAAK,QAAQ,CAAC,UAAU;gBAC9C,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,MAAM,YAAY,iBAAiB;IACnC,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,WAAa,AAAD,EAAE;IAE/B,sCAAsC;IACtC,IAAI,gBAAgB,QAAQ,CAAC,YAAY;QACvC,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,eAAe,QAAQ,CAAC,WAAW;QACrC,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,eAAe,WAAW;QAC5B,OAAO;IACT;IAEA,gEAAgE;IAChE,OACE,gBAAgB,QAAQ,CAAC,cACzB,SAAS,WAAW,GAAG,QAAQ,CAAC,aAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,cAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,gBAChC,SAAS,WAAW,GAAG,QAAQ,CAAC,eAChC,aAAa,gBACb,aAAa,wBACb,aAAa;AAEjB;AAEO,SAAS,gBAAgB,QAAgB;IAC9C,yDAAyD;IACzD,OAAO,iBAAiB;AAC1B;AAGO,SAAS,UACd,OAAe,EACf,eAAuB,IAAI;IAE3B,MAAM,QAAQ,QAAQ,KAAK,CAAC;IAC5B,MAAM,SAAmB,EAAE;IAC3B,IAAI,eAAe;IAEnB,KAAK,MAAM,QAAQ,MAAO;QACxB,IACE,aAAa,MAAM,GAAG,KAAK,MAAM,GAAG,IAAI,gBACxC,aAAa,MAAM,GAAG,GACtB;YACA,OAAO,IAAI,CAAC,aAAa,IAAI;YAC7B,eAAe;QACjB,OAAO;YACL,gBAAgB,CAAC,eAAe,OAAO,EAAE,IAAI;QAC/C;IACF;IAEA,IAAI,aAAa,IAAI,IAAI;QACvB,OAAO,IAAI,CAAC,aAAa,IAAI;IAC/B;IAEA,OAAO,OAAO,MAAM,CAAC,CAAC,QAAU,MAAM,MAAM,GAAG;AACjD;AAGO,SAAS,mBACd,OAAe,EACf,QAAgB;IAEhB,MAAM,UAAoB,EAAE;IAE5B,IAAI;QACF,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,4CAA4C;gBAC5C,MAAM,aAAa;oBACjB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,WAAY;oBAChC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;YAEF,KAAK;gBACH,0CAA0C;gBAC1C,MAAM,aAAa;oBACjB;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,WAAY;oBAChC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;YAEF,KAAK;gBACH,sCAAsC;gBACtC,MAAM,eAAe;oBACnB;oBACA;oBACA;oBACA;oBACA;iBACD;gBAED,KAAK,MAAM,WAAW,aAAc;oBAClC,IAAI;oBACJ,MAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,KAAM;wBAC/C,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;oBACvB;gBACF;gBACA;QACJ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;IAEA,OAAO;WAAI,IAAI,IAAI;KAAS,EAAE,oBAAoB;AACpD;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 dark:focus-visible:ring-blue-400 focus-visible:ring-offset-2 focus-visible:ring-offset-white dark:focus-visible:ring-offset-slate-800 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-slate-900 dark:bg-slate-100 text-white dark:text-slate-900 hover:bg-slate-800 dark:hover:bg-slate-200 shadow-sm hover:shadow-md\",\n        destructive:\n          \"bg-red-600 text-white hover:bg-red-700 shadow-sm hover:shadow-md\",\n        outline:\n          \"border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 hover:bg-slate-50 dark:hover:bg-slate-700 shadow-sm hover:shadow-md\",\n        secondary:\n          \"bg-slate-100 dark:bg-slate-800 text-slate-900 dark:text-slate-100 hover:bg-slate-200 dark:hover:bg-slate-700 shadow-sm\",\n        ghost: \"hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-slate-100\",\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-lg px-3\",\n        lg: \"h-12 rounded-lg px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,2XACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 px-3 py-2 text-sm shadow-sm transition-all hover:border-slate-300 dark:hover:border-slate-600 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-500 dark:placeholder:text-slate-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 dark:focus-visible:ring-blue-400 focus-visible:ring-offset-2 focus-visible:ring-offset-white dark:focus-visible:ring-offset-slate-800 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mkBACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100 shadow-sm hover:shadow-md transition-shadow duration-200\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-slate-600 dark:text-slate-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mLACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 625, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/navigation/navbar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Code, Settings, Home, Github } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\n\nexport function Navbar() {\n  const pathname = usePathname()\n\n  const navigation = [\n    { name: 'Projects', href: '/', icon: Home },\n    { name: 'Settings', href: '/settings', icon: Settings },\n  ]\n\n  return (\n    <nav className=\"border-b border-slate-200/50 dark:border-slate-700/50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-slate-900/60 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          <div className=\"flex items-center gap-8\">\n            <Link href=\"/\" className=\"flex items-center gap-3 group\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200\">\n                <Code className=\"h-4 w-4 text-white\" />\n              </div>\n              <span className=\"font-bold text-xl bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent\">\n                Code Index\n              </span>\n            </Link>\n\n            <div className=\"hidden md:flex items-center gap-1\">\n              {navigation.map((item) => {\n                const Icon = item.icon\n                const isActive = pathname === item.href\n\n                return (\n                  <Link key={item.name} href={item.href}>\n                    <Button\n                      variant={isActive ? 'secondary' : 'ghost'}\n                      size=\"sm\"\n                      className={cn(\n                        'flex items-center gap-2 transition-all duration-200',\n                        isActive\n                          ? 'bg-blue-50 dark:bg-blue-950/50 text-blue-700 dark:text-blue-300 shadow-sm'\n                          : 'hover:bg-slate-50 dark:hover:bg-slate-800'\n                      )}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                      {item.name}\n                    </Button>\n                  </Link>\n                )\n              })}\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-4\">\n            <div className=\"hidden sm:flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span>AI-Powered Analysis</span>\n            </div>\n\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"hidden sm:flex border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800\"\n                asChild\n              >\n                <a href=\"https://github.com\" target=\"_blank\" rel=\"noopener noreferrer\">\n                  <Github className=\"h-4 w-4 mr-2\" />\n                  GitHub\n                </a>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAY,MAAM;YAAK,MAAM,sMAAA,CAAA,OAAI;QAAC;QAC1C;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAK,WAAU;kDAAqI;;;;;;;;;;;;0CAKvJ,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,OAAO,KAAK,IAAI;oCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wCAAiB,MAAM,KAAK,IAAI;kDACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,cAAc;4CAClC,MAAK;4CACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uDACA,WACI,8EACA;;8DAGN,6LAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAZH,KAAK,IAAI;;;;;gCAgBxB;;;;;;;;;;;;kCAIJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,OAAO;8CAEP,cAAA,6LAAC;wCAAE,MAAK;wCAAqB,QAAO;wCAAS,KAAI;;0DAC/C,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD;GAxEgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2KACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/projects/file-upload-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { \n  Dialog, \n  DialogContent, \n  DialogDescription, \n  DialogHeader, \n  DialogTitle \n} from '@/components/ui/dialog'\nimport { Upload, File, Folder, X, CheckCircle, AlertCircle, Loader2, Info } from 'lucide-react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { shouldUploadFile } from '@/lib/utils'\n\ninterface FileUploadDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  projectId: string\n  onUploadComplete: () => void\n}\n\ninterface UploadFile {\n  file: File\n  path: string\n  status: 'pending' | 'uploading' | 'completed' | 'error'\n  progress: number\n  error?: string\n}\n\nexport function FileUploadDialog({ open, onOpenChange, projectId, onUploadComplete }: FileUploadDialogProps) {\n  const [files, setFiles] = useState<UploadFile[]>([])\n  const [isDragOver, setIsDragOver] = useState(false)\n  const [isUploading, setIsUploading] = useState(false)\n  const [uploadProgress, setUploadProgress] = useState(0)\n  const [filteredCount, setFilteredCount] = useState(0)\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(true)\n  }, [])\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(false)\n  }, [])\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault()\n    setIsDragOver(false)\n    \n    const items = Array.from(e.dataTransfer.items)\n    processDroppedItems(items)\n  }, [])\n\n  const processDroppedItems = async (items: DataTransferItem[]) => {\n    const newFiles: UploadFile[] = []\n    let filtered = 0\n\n    for (const item of items) {\n      if (item.kind === 'file') {\n        const entry = item.webkitGetAsEntry()\n        if (entry) {\n          await processEntry(entry, '', newFiles, (count) => { filtered += count })\n        }\n      }\n    }\n\n    setFiles(prev => [...prev, ...newFiles])\n    setFilteredCount(prev => prev + filtered)\n  }\n\n  const processEntry = async (entry: any, path: string, fileList: UploadFile[], onFiltered?: (count: number) => void) => {\n    if (entry.isFile) {\n      entry.file((file: File) => {\n        const fullPath = path ? `${path}/${file.name}` : file.name\n\n        // Apply filtering\n        if (shouldUploadFile(fullPath)) {\n          fileList.push({\n            file,\n            path: fullPath,\n            status: 'pending',\n            progress: 0\n          })\n        } else {\n          // Count filtered files\n          onFiltered?.(1)\n        }\n      })\n    } else if (entry.isDirectory) {\n      const reader = entry.createReader()\n      const entries = await new Promise<any[]>((resolve) => {\n        reader.readEntries(resolve)\n      })\n\n      for (const childEntry of entries) {\n        const newPath = path ? `${path}/${entry.name}` : entry.name\n        await processEntry(childEntry, newPath, fileList, onFiltered)\n      }\n    }\n  }\n\n  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFiles = Array.from(e.target.files || [])\n    let filtered = 0\n\n    const newFiles: UploadFile[] = selectedFiles\n      .filter(file => {\n        if (shouldUploadFile(file.name)) {\n          return true\n        } else {\n          filtered++\n          return false\n        }\n      })\n      .map(file => ({\n        file,\n        path: file.name,\n        status: 'pending',\n        progress: 0\n      }))\n\n    setFiles(prev => [...prev, ...newFiles])\n    setFilteredCount(prev => prev + filtered)\n  }\n\n  const removeFile = (index: number) => {\n    setFiles(prev => prev.filter((_, i) => i !== index))\n  }\n\n  const uploadFiles = async () => {\n    if (files.length === 0) return\n    \n    setIsUploading(true)\n    setUploadProgress(0)\n    \n    try {\n      const formData = new FormData()\n      \n      files.forEach((uploadFile, index) => {\n        formData.append('files', uploadFile.file)\n        formData.append('paths', uploadFile.path)\n      })\n      \n      const response = await fetch(`/api/projects/${projectId}/upload`, {\n        method: 'POST',\n        body: formData,\n      })\n      \n      if (response.ok) {\n        const result = await response.json()\n        \n        // Update file statuses\n        setFiles(prev => prev.map(file => ({\n          ...file,\n          status: 'completed',\n          progress: 100\n        })))\n        \n        // Start indexing\n        await fetch(`/api/projects/${projectId}/index`, {\n          method: 'POST',\n        })\n        \n        onUploadComplete()\n        \n        // Close dialog after a short delay\n        setTimeout(() => {\n          onOpenChange(false)\n          setFiles([])\n        }, 1500)\n      } else {\n        throw new Error('Upload failed')\n      }\n    } catch (error) {\n      console.error('Upload error:', error)\n      setFiles(prev => prev.map(file => ({\n        ...file,\n        status: 'error',\n        error: 'Upload failed'\n      })))\n    } finally {\n      setIsUploading(false)\n    }\n  }\n\n  const getStatusIcon = (status: UploadFile['status']) => {\n    switch (status) {\n      case 'pending':\n        return <File className=\"w-4 h-4 text-slate-400\" />\n      case 'uploading':\n        return <Loader2 className=\"w-4 h-4 text-blue-500 animate-spin\" />\n      case 'completed':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      case 'error':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />\n    }\n  }\n\n  const getStatusColor = (status: UploadFile['status']) => {\n    switch (status) {\n      case 'pending':\n        return 'bg-slate-100 text-slate-700'\n      case 'uploading':\n        return 'bg-blue-100 text-blue-700'\n      case 'completed':\n        return 'bg-green-100 text-green-700'\n      case 'error':\n        return 'bg-red-100 text-red-700'\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-hidden flex flex-col\">\n        <DialogHeader>\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-4\">\n            <Upload className=\"w-6 h-6 text-white\" />\n          </div>\n          <DialogTitle className=\"text-2xl text-center\">Upload Files</DialogTitle>\n          <DialogDescription className=\"text-center\">\n            Add new files or update existing ones in your project.<br />\n            <span className=\"text-xs text-slate-500\">\n              Files like node_modules, build outputs, and lock files are automatically filtered out.\n            </span>\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"flex-1 overflow-hidden flex flex-col\">\n          {/* Drop Zone */}\n          <div\n            className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${\n              isDragOver \n                ? 'border-blue-400 bg-blue-50 dark:bg-blue-950/20' \n                : 'border-slate-300 dark:border-slate-600'\n            }`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <Upload className=\"w-12 h-12 text-slate-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Drop files or folders here</h3>\n            <p className=\"text-slate-600 dark:text-slate-400 mb-4\">\n              Or click to select files manually\n            </p>\n            <input\n              type=\"file\"\n              multiple\n              onChange={handleFileSelect}\n              className=\"hidden\"\n              id=\"file-upload\"\n            />\n            <div className=\"flex gap-2 justify-center\">\n              <Button\n                variant=\"outline\"\n                onClick={() => document.getElementById('file-upload')?.click()}\n              >\n                <File className=\"w-4 h-4 mr-2\" />\n                Select Files\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  const input = document.createElement('input')\n                  input.type = 'file'\n                  input.multiple = true\n                  ;(input as any).webkitdirectory = true\n                  input.onchange = (e) => {\n                    const files = Array.from((e.target as HTMLInputElement).files || [])\n                    let filtered = 0\n\n                    const newFiles: UploadFile[] = files\n                      .filter(file => {\n                        const filePath = file.webkitRelativePath || file.name\n                        if (shouldUploadFile(filePath)) {\n                          return true\n                        } else {\n                          filtered++\n                          return false\n                        }\n                      })\n                      .map(file => ({\n                        file,\n                        path: file.webkitRelativePath || file.name,\n                        status: 'pending',\n                        progress: 0\n                      }))\n\n                    setFiles(prev => [...prev, ...newFiles])\n                    setFilteredCount(prev => prev + filtered)\n                  }\n                  input.click()\n                }}\n              >\n                <Folder className=\"w-4 h-4 mr-2\" />\n                Select Folder\n              </Button>\n            </div>\n          </div>\n\n          {/* Filtered Files Info */}\n          {filteredCount > 0 && (\n            <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n              <div className=\"flex items-center gap-2 text-sm text-blue-700 dark:text-blue-300\">\n                <Info className=\"w-4 h-4\" />\n                <span>\n                  {filteredCount} file{filteredCount !== 1 ? 's' : ''} filtered out (node_modules, build files, etc.)\n                </span>\n              </div>\n            </div>\n          )}\n\n          {/* File List */}\n          {files.length > 0 && (\n            <div className=\"mt-6 flex-1 overflow-hidden\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h4 className=\"font-semibold\">Files to Upload ({files.length})</h4>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => {\n                    setFiles([])\n                    setFilteredCount(0)\n                  }}\n                  disabled={isUploading}\n                >\n                  Clear All\n                </Button>\n              </div>\n              \n              <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n                {files.map((uploadFile, index) => (\n                  <Card key={index} className=\"p-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                        {getStatusIcon(uploadFile.status)}\n                        <div className=\"flex-1 min-w-0\">\n                          <p className=\"text-sm font-medium truncate\">{uploadFile.path}</p>\n                          <p className=\"text-xs text-slate-500\">\n                            {(uploadFile.file.size / 1024).toFixed(1)} KB\n                          </p>\n                        </div>\n                        <Badge variant=\"secondary\" className={getStatusColor(uploadFile.status)}>\n                          {uploadFile.status}\n                        </Badge>\n                      </div>\n                      {!isUploading && uploadFile.status === 'pending' && (\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={() => removeFile(index)}\n                        >\n                          <X className=\"w-4 h-4\" />\n                        </Button>\n                      )}\n                    </div>\n                    {uploadFile.error && (\n                      <p className=\"text-xs text-red-500 mt-1\">{uploadFile.error}</p>\n                    )}\n                  </Card>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex gap-3 pt-4 border-t\">\n          <Button\n            variant=\"outline\"\n            onClick={() => onOpenChange(false)}\n            disabled={isUploading}\n            className=\"flex-1\"\n          >\n            Cancel\n          </Button>\n          <Button\n            onClick={uploadFiles}\n            disabled={files.length === 0 || isUploading}\n            className=\"flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\n          >\n            {isUploading ? (\n              <>\n                <Loader2 className=\"w-4 h-4 mr-2 animate-spin\" />\n                Uploading...\n              </>\n            ) : (\n              <>\n                <Upload className=\"w-4 h-4 mr-2\" />\n                Upload & Index\n              </>\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAdA;;;;;;;;AA+BO,SAAS,iBAAiB,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,gBAAgB,EAAyB;;IACzG,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAClC,EAAE,cAAc;YAChB,cAAc;QAChB;uDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACnC,EAAE,cAAc;YAChB,cAAc;QAChB;wDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,cAAc;YAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;YAC7C,oBAAoB;QACtB;mDAAG,EAAE;IAEL,MAAM,sBAAsB,OAAO;QACjC,MAAM,WAAyB,EAAE;QACjC,IAAI,WAAW;QAEf,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,KAAK,IAAI,KAAK,QAAQ;gBACxB,MAAM,QAAQ,KAAK,gBAAgB;gBACnC,IAAI,OAAO;oBACT,MAAM,aAAa,OAAO,IAAI,UAAU,CAAC;wBAAY,YAAY;oBAAM;gBACzE;YACF;QACF;QAEA,SAAS,CAAA,OAAQ;mBAAI;mBAAS;aAAS;QACvC,iBAAiB,CAAA,OAAQ,OAAO;IAClC;IAEA,MAAM,eAAe,OAAO,OAAY,MAAc,UAAwB;QAC5E,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,IAAI,CAAC,CAAC;gBACV,MAAM,WAAW,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI;gBAE1D,kBAAkB;gBAClB,IAAI,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;oBAC9B,SAAS,IAAI,CAAC;wBACZ;wBACA,MAAM;wBACN,QAAQ;wBACR,UAAU;oBACZ;gBACF,OAAO;oBACL,uBAAuB;oBACvB,aAAa;gBACf;YACF;QACF,OAAO,IAAI,MAAM,WAAW,EAAE;YAC5B,MAAM,SAAS,MAAM,YAAY;YACjC,MAAM,UAAU,MAAM,IAAI,QAAe,CAAC;gBACxC,OAAO,WAAW,CAAC;YACrB;YAEA,KAAK,MAAM,cAAc,QAAS;gBAChC,MAAM,UAAU,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,IAAI,EAAE,GAAG,MAAM,IAAI;gBAC3D,MAAM,aAAa,YAAY,SAAS,UAAU;YACpD;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,gBAAgB,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QACrD,IAAI,WAAW;QAEf,MAAM,WAAyB,cAC5B,MAAM,CAAC,CAAA;YACN,IAAI,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,IAAI,GAAG;gBAC/B,OAAO;YACT,OAAO;gBACL;gBACA,OAAO;YACT;QACF,GACC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ;gBACA,MAAM,KAAK,IAAI;gBACf,QAAQ;gBACR,UAAU;YACZ,CAAC;QAEH,SAAS,CAAA,OAAQ;mBAAI;mBAAS;aAAS;QACvC,iBAAiB,CAAA,OAAQ,OAAO;IAClC;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IAC/C;IAEA,MAAM,cAAc;QAClB,IAAI,MAAM,MAAM,KAAK,GAAG;QAExB,eAAe;QACf,kBAAkB;QAElB,IAAI;YACF,MAAM,WAAW,IAAI;YAErB,MAAM,OAAO,CAAC,CAAC,YAAY;gBACzB,SAAS,MAAM,CAAC,SAAS,WAAW,IAAI;gBACxC,SAAS,MAAM,CAAC,SAAS,WAAW,IAAI;YAC1C;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,OAAO,CAAC,EAAE;gBAChE,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,uBAAuB;gBACvB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;4BACjC,GAAG,IAAI;4BACP,QAAQ;4BACR,UAAU;wBACZ,CAAC;gBAED,iBAAiB;gBACjB,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,MAAM,CAAC,EAAE;oBAC9C,QAAQ;gBACV;gBAEA;gBAEA,mCAAmC;gBACnC,WAAW;oBACT,aAAa;oBACb,SAAS,EAAE;gBACb,GAAG;YACL,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACjC,GAAG,IAAI;wBACP,QAAQ;wBACR,OAAO;oBACT,CAAC;QACH,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAuB;;;;;;sCAC9C,6LAAC,qIAAA,CAAA,oBAAiB;4BAAC,WAAU;;gCAAc;8CACa,6LAAC;;;;;8CACvD,6LAAC;oCAAK,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;8BAM7C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,mDACA,0CACJ;4BACF,YAAY;4BACZ,aAAa;4BACb,QAAQ;;8CAER,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;8CAGvD,6LAAC;oCACC,MAAK;oCACL,QAAQ;oCACR,UAAU;oCACV,WAAU;oCACV,IAAG;;;;;;8CAEL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,SAAS,cAAc,CAAC,gBAAgB;;8DAEvD,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;gDACP,MAAM,QAAQ,SAAS,aAAa,CAAC;gDACrC,MAAM,IAAI,GAAG;gDACb,MAAM,QAAQ,GAAG;gDACf,MAAc,eAAe,GAAG;gDAClC,MAAM,QAAQ,GAAG,CAAC;oDAChB,MAAM,QAAQ,MAAM,IAAI,CAAC,AAAC,EAAE,MAAM,CAAsB,KAAK,IAAI,EAAE;oDACnE,IAAI,WAAW;oDAEf,MAAM,WAAyB,MAC5B,MAAM,CAAC,CAAA;wDACN,MAAM,WAAW,KAAK,kBAAkB,IAAI,KAAK,IAAI;wDACrD,IAAI,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;4DAC9B,OAAO;wDACT,OAAO;4DACL;4DACA,OAAO;wDACT;oDACF,GACC,GAAG,CAAC,CAAA,OAAQ,CAAC;4DACZ;4DACA,MAAM,KAAK,kBAAkB,IAAI,KAAK,IAAI;4DAC1C,QAAQ;4DACR,UAAU;wDACZ,CAAC;oDAEH,SAAS,CAAA,OAAQ;+DAAI;+DAAS;yDAAS;oDACvC,iBAAiB,CAAA,OAAQ,OAAO;gDAClC;gDACA,MAAM,KAAK;4CACb;;8DAEA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;wBAOxC,gBAAgB,mBACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;;4CACE;4CAAc;4CAAM,kBAAkB,IAAI,MAAM;4CAAG;;;;;;;;;;;;;;;;;;wBAO3D,MAAM,MAAM,GAAG,mBACd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAgB;gDAAkB,MAAM,MAAM;gDAAC;;;;;;;sDAC7D,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,SAAS,EAAE;gDACX,iBAAiB;4CACnB;4CACA,UAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,YAAY,sBACtB,6LAAC,mIAAA,CAAA,OAAI;4CAAa,WAAU;;8DAC1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,WAAW,MAAM;8EAChC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAgC,WAAW,IAAI;;;;;;sFAC5D,6LAAC;4EAAE,WAAU;;gFACV,CAAC,WAAW,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;gFAAG;;;;;;;;;;;;;8EAG9C,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAW,eAAe,WAAW,MAAM;8EACnE,WAAW,MAAM;;;;;;;;;;;;wDAGrB,CAAC,eAAe,WAAW,MAAM,KAAK,2BACrC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,WAAW;sEAE1B,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAIlB,WAAW,KAAK,kBACf,6LAAC;oDAAE,WAAU;8DAA6B,WAAW,KAAK;;;;;;;2CAzBnD;;;;;;;;;;;;;;;;;;;;;;8BAmCrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,MAAM,MAAM,KAAK,KAAK;4BAChC,WAAU;sCAET,4BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;6DAInD;;kDACE,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASnD;GAhXgB;KAAA", "debugId": null}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 1752, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/projects/indexing-progress.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Progress } from '@/components/ui/progress'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { \n  RefreshCw, \n  CheckCircle, \n  AlertCircle, \n  FileText, \n  Zap, \n  Clock,\n  X\n} from 'lucide-react'\n\ninterface IndexingProgressProps {\n  projectId: string\n  isIndexing: boolean\n  onComplete?: () => void\n  onClose?: () => void\n}\n\ninterface IndexingStatus {\n  status: 'scanning' | 'indexing' | 'completed' | 'failed'\n  totalFiles: number\n  processedFiles: number\n  currentFile: string\n  error?: string\n  startTime?: string\n  estimatedTimeRemaining?: number\n}\n\nexport function IndexingProgress({ projectId, isIndexing, onComplete, onClose }: IndexingProgressProps) {\n  const [progress, setProgress] = useState<IndexingStatus>({\n    status: 'scanning',\n    totalFiles: 0,\n    processedFiles: 0,\n    currentFile: 'Initializing...',\n  })\n  const [startTime, setStartTime] = useState<Date | null>(null)\n  const [elapsedTime, setElapsedTime] = useState(0)\n\n  useEffect(() => {\n    if (isIndexing && !startTime) {\n      setStartTime(new Date())\n    }\n  }, [isIndexing, startTime])\n\n  useEffect(() => {\n    let interval: NodeJS.Timeout\n    if (isIndexing && startTime) {\n      interval = setInterval(() => {\n        setElapsedTime(Math.floor((Date.now() - startTime.getTime()) / 1000))\n      }, 1000)\n    }\n    return () => {\n      if (interval) clearInterval(interval)\n    }\n  }, [isIndexing, startTime])\n\n  useEffect(() => {\n    if (!isIndexing) return\n\n    const pollProgress = async () => {\n      try {\n        const response = await fetch(`/api/projects/${projectId}/index/progress`)\n        if (response.ok) {\n          const data = await response.json()\n          setProgress(data.progress)\n          \n          if (data.progress.status === 'completed' || data.progress.status === 'failed') {\n            onComplete?.()\n          }\n        }\n      } catch (error) {\n        console.error('Error polling progress:', error)\n      }\n    }\n\n    // Poll every 500ms for smooth progress updates\n    const interval = setInterval(pollProgress, 500)\n    \n    // Initial poll\n    pollProgress()\n\n    return () => clearInterval(interval)\n  }, [isIndexing, projectId, onComplete])\n\n  if (!isIndexing) return null\n\n  const progressPercentage = progress.totalFiles > 0 \n    ? Math.round((progress.processedFiles / progress.totalFiles) * 100)\n    : 0\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getStatusIcon = () => {\n    switch (progress.status) {\n      case 'scanning':\n        return <RefreshCw className=\"w-5 h-5 text-blue-500 animate-spin\" />\n      case 'indexing':\n        return <Zap className=\"w-5 h-5 text-yellow-500\" />\n      case 'completed':\n        return <CheckCircle className=\"w-5 h-5 text-green-500\" />\n      case 'failed':\n        return <AlertCircle className=\"w-5 h-5 text-red-500\" />\n    }\n  }\n\n  const getStatusText = () => {\n    switch (progress.status) {\n      case 'scanning':\n        return 'Scanning files...'\n      case 'indexing':\n        return 'Indexing files...'\n      case 'completed':\n        return 'Indexing completed!'\n      case 'failed':\n        return 'Indexing failed'\n    }\n  }\n\n  const getStatusColor = () => {\n    switch (progress.status) {\n      case 'scanning':\n        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300'\n      case 'indexing':\n        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/50 dark:text-yellow-300'\n      case 'completed':\n        return 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300'\n      case 'failed':\n        return 'bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-300'\n    }\n  }\n\n  return (\n    <Card className=\"border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-950/20 dark:to-purple-950/20\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            {getStatusIcon()}\n            <div>\n              <CardTitle className=\"text-lg\">{getStatusText()}</CardTitle>\n              <div className=\"flex items-center gap-2 mt-1\">\n                <Badge className={getStatusColor()}>\n                  {progress.status}\n                </Badge>\n                {elapsedTime > 0 && (\n                  <div className=\"flex items-center gap-1 text-sm text-slate-500 dark:text-slate-400\">\n                    <Clock className=\"w-3 h-3\" />\n                    {formatTime(elapsedTime)}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n          {onClose && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={onClose}\n              className=\"h-8 w-8 p-0\"\n            >\n              <X className=\"w-4 h-4\" />\n            </Button>\n          )}\n        </div>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Progress Bar */}\n        {progress.totalFiles > 0 && (\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center justify-between text-sm\">\n              <span className=\"text-slate-600 dark:text-slate-400\">\n                Progress: {progress.processedFiles} / {progress.totalFiles} files\n              </span>\n              <span className=\"font-semibold text-slate-900 dark:text-slate-100\">\n                {progressPercentage}%\n              </span>\n            </div>\n            <Progress \n              value={progressPercentage} \n              className=\"h-2\"\n            />\n          </div>\n        )}\n\n        {/* Current File */}\n        {progress.currentFile && progress.status === 'indexing' && (\n          <div className=\"flex items-center gap-2 text-sm\">\n            <FileText className=\"w-4 h-4 text-slate-400\" />\n            <span className=\"text-slate-600 dark:text-slate-400\">\n              Processing:\n            </span>\n            <span className=\"font-mono text-slate-900 dark:text-slate-100 truncate\">\n              {progress.currentFile}\n            </span>\n          </div>\n        )}\n\n        {/* Error Message */}\n        {progress.error && (\n          <div className=\"bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-lg p-3\">\n            <div className=\"flex items-center gap-2 text-red-700 dark:text-red-300\">\n              <AlertCircle className=\"w-4 h-4\" />\n              <span className=\"font-semibold\">Error:</span>\n            </div>\n            <p className=\"text-red-600 dark:text-red-400 text-sm mt-1\">\n              {progress.error}\n            </p>\n          </div>\n        )}\n\n        {/* Success Message */}\n        {progress.status === 'completed' && (\n          <div className=\"bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-3\">\n            <div className=\"flex items-center gap-2 text-green-700 dark:text-green-300\">\n              <CheckCircle className=\"w-4 h-4\" />\n              <span className=\"font-semibold\">Success!</span>\n            </div>\n            <p className=\"text-green-600 dark:text-green-400 text-sm mt-1\">\n              Successfully indexed {progress.processedFiles} files in {formatTime(elapsedTime)}\n            </p>\n          </div>\n        )}\n\n        {/* Stats */}\n        {progress.totalFiles > 0 && (\n          <div className=\"grid grid-cols-2 gap-4 pt-2 border-t\">\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-slate-900 dark:text-slate-100\">\n                {progress.totalFiles}\n              </div>\n              <div className=\"text-xs text-slate-500 dark:text-slate-400\">\n                Total Files\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-lg font-bold text-slate-900 dark:text-slate-100\">\n                {progress.processedFiles}\n              </div>\n              <div className=\"text-xs text-slate-500 dark:text-slate-400\">\n                Processed\n              </div>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAkCO,SAAS,iBAAiB,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAyB;;IACpG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,QAAQ;QACR,YAAY;QACZ,gBAAgB;QAChB,aAAa;IACf;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,cAAc,CAAC,WAAW;gBAC5B,aAAa,IAAI;YACnB;QACF;qCAAG;QAAC;QAAY;KAAU;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI;YACJ,IAAI,cAAc,WAAW;gBAC3B,WAAW;kDAAY;wBACrB,eAAe,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,UAAU,OAAO,EAAE,IAAI;oBACjE;iDAAG;YACL;YACA;8CAAO;oBACL,IAAI,UAAU,cAAc;gBAC9B;;QACF;qCAAG;QAAC;QAAY;KAAU;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,YAAY;YAEjB,MAAM;2DAAe;oBACnB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,eAAe,CAAC;wBACxE,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,YAAY,KAAK,QAAQ;4BAEzB,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,eAAe,KAAK,QAAQ,CAAC,MAAM,KAAK,UAAU;gCAC7E;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C;gBACF;;YAEA,+CAA+C;YAC/C,MAAM,WAAW,YAAY,cAAc;YAE3C,eAAe;YACf;YAEA;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;QAAY;QAAW;KAAW;IAEtC,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,qBAAqB,SAAS,UAAU,GAAG,IAC7C,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI,OAC7D;IAEJ,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,gBAAgB;QACpB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ,SAAS,MAAM;YACrB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ;8CACD,6LAAC;;sDACC,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAW;;;;;;sDAChC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,WAAW;8DACf,SAAS,MAAM;;;;;;gDAEjB,cAAc,mBACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;wBAMrB,yBACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAKrB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,SAAS,UAAU,GAAG,mBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;4CAAqC;4CACxC,SAAS,cAAc;4CAAC;4CAAI,SAAS,UAAU;4CAAC;;;;;;;kDAE7D,6LAAC;wCAAK,WAAU;;4CACb;4CAAmB;;;;;;;;;;;;;0CAGxB,6LAAC,uIAAA,CAAA,WAAQ;gCACP,OAAO;gCACP,WAAU;;;;;;;;;;;;oBAMf,SAAS,WAAW,IAAI,SAAS,MAAM,KAAK,4BAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAAqC;;;;;;0CAGrD,6LAAC;gCAAK,WAAU;0CACb,SAAS,WAAW;;;;;;;;;;;;oBAM1B,SAAS,KAAK,kBACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CACV,SAAS,KAAK;;;;;;;;;;;;oBAMpB,SAAS,MAAM,KAAK,6BACnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;;oCAAkD;oCACvC,SAAS,cAAc;oCAAC;oCAAW,WAAW;;;;;;;;;;;;;oBAMzE,SAAS,UAAU,GAAG,mBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,UAAU;;;;;;kDAEtB,6LAAC;wCAAI,WAAU;kDAA6C;;;;;;;;;;;;0CAI9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,SAAS,cAAc;;;;;;kDAE1B,6LAAC;wCAAI,WAAU;kDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E;GA9NgB;KAAA", "debugId": null}}, {"offset": {"line": 2270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/components/projects/project-chat-page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { ArrowLeft, Send, Settings, BarChart3, FileText, Code, Zap, Play, RefreshCw, Upload, FolderOpen, Clock } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Navbar } from '@/components/navigation/navbar'\nimport { FileUploadDialog } from './file-upload-dialog'\nimport { IndexingProgress } from './indexing-progress'\nimport Link from 'next/link'\n\ninterface Project {\n  id: string\n  name: string\n  description?: string\n  path: string\n  language?: string\n  framework?: string\n  isIndexed: boolean\n  indexingStatus: string\n  totalFiles: number\n  indexedFiles: number\n  totalLines: number\n  createdAt: string\n  lastIndexedAt?: string\n}\n\ninterface ChatMessage {\n  id: string\n  role: 'user' | 'assistant'\n  content: string\n  timestamp: string\n}\n\ninterface ProjectFile {\n  id: string\n  path: string\n  name: string\n  extension?: string\n  size: number\n  lines: number\n  isIndexed: boolean\n  lastIndexedAt?: string\n}\n\ninterface ProjectChatPageProps {\n  projectId: string\n}\n\nexport function ProjectChatPage({ projectId }: ProjectChatPageProps) {\n  const [project, setProject] = useState<Project | null>(null)\n  const [messages, setMessages] = useState<ChatMessage[]>([])\n  const [inputMessage, setInputMessage] = useState('')\n  const [isLoading, setIsLoading] = useState(true)\n  const [isSending, setIsSending] = useState(false)\n  const [activeTab, setActiveTab] = useState<'chat' | 'stats' | 'files'>('chat')\n  const [isIndexing, setIsIndexing] = useState(false)\n  const [files, setFiles] = useState<ProjectFile[]>([])\n  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)\n  const [isLoadingFiles, setIsLoadingFiles] = useState(false)\n\n  useEffect(() => {\n    fetchProject()\n    if (activeTab === 'files') {\n      fetchFiles()\n    }\n  }, [projectId, activeTab])\n\n  const fetchProject = async () => {\n    try {\n      const response = await fetch(`/api/projects/${projectId}`)\n      if (response.ok) {\n        const data = await response.json()\n        setProject(data.project)\n      }\n    } catch (error) {\n      console.error('Error fetching project:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const fetchFiles = async () => {\n    setIsLoadingFiles(true)\n    try {\n      const response = await fetch(`/api/projects/${projectId}/index`)\n      if (response.ok) {\n        const data = await response.json()\n        setFiles(data.files || [])\n      }\n    } catch (error) {\n      console.error('Error fetching files:', error)\n    } finally {\n      setIsLoadingFiles(false)\n    }\n  }\n\n  const sendMessage = async () => {\n    if (!inputMessage.trim() || isSending) return\n\n    const userMessage: ChatMessage = {\n      id: Date.now().toString(),\n      role: 'user',\n      content: inputMessage,\n      timestamp: new Date().toISOString(),\n    }\n\n    setMessages(prev => [...prev, userMessage])\n    setInputMessage('')\n    setIsSending(true)\n\n    try {\n      const response = await fetch(`/api/projects/${projectId}/chat`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: inputMessage,\n          history: messages,\n        }),\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        const assistantMessage: ChatMessage = {\n          id: (Date.now() + 1).toString(),\n          role: 'assistant',\n          content: data.response,\n          timestamp: new Date().toISOString(),\n        }\n        setMessages(prev => [...prev, assistantMessage])\n      }\n    } catch (error) {\n      console.error('Error sending message:', error)\n    } finally {\n      setIsSending(false)\n    }\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      sendMessage()\n    }\n  }\n\n  const startIndexing = async (incremental: boolean = false) => {\n    if (!project || isIndexing) return\n\n    setIsIndexing(true)\n    try {\n      const response = await fetch(`/api/projects/${projectId}/index`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ incremental }),\n      })\n\n      if (response.ok) {\n        // Poll for indexing status\n        const pollStatus = setInterval(async () => {\n          const statusResponse = await fetch(`/api/projects/${projectId}`)\n          if (statusResponse.ok) {\n            const data = await statusResponse.json()\n            setProject(data.project)\n\n            if (data.project.indexingStatus !== 'indexing') {\n              clearInterval(pollStatus)\n              setIsIndexing(false)\n              if (activeTab === 'files') {\n                fetchFiles() // Refresh files after indexing\n              }\n            }\n          }\n        }, 2000)\n      }\n    } catch (error) {\n      console.error('Error starting indexing:', error)\n      setIsIndexing(false)\n    }\n  }\n\n  const handleUploadComplete = () => {\n    fetchProject()\n    if (activeTab === 'files') {\n      fetchFiles()\n    }\n    // Start incremental indexing after upload\n    setTimeout(() => {\n      startIndexing(true)\n    }, 1000)\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p>Loading project...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!project) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-2\">Project not found</h2>\n          <p className=\"text-muted-foreground mb-4\">The project you're looking for doesn't exist.</p>\n          <Link href=\"/\">\n            <Button>\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              Back to Projects\n            </Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background flex flex-col\">\n      <Navbar />\n      {/* Header */}\n      <div className=\"border-b\">\n        <div className=\"container mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  <ArrowLeft className=\"w-4 h-4 mr-2\" />\n                  Back\n                </Button>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold\">{project.name}</h1>\n                <div className=\"flex items-center gap-2 mt-1\">\n                  {project.language && <Badge variant=\"secondary\">{project.language}</Badge>}\n                  {project.framework && <Badge variant=\"outline\">{project.framework}</Badge>}\n                  <Badge variant={project.isIndexed ? 'default' : 'secondary'}>\n                    {project.indexingStatus === 'indexing' ? 'Indexing...' :\n                     project.isIndexed ? 'Indexed' : 'Not Indexed'}\n                  </Badge>\n                  {!project.isIndexed && project.indexingStatus !== 'indexing' && (\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={startIndexing}\n                      disabled={isIndexing}\n                    >\n                      <Play className=\"w-4 h-4 mr-2\" />\n                      Start Indexing\n                    </Button>\n                  )}\n                  {project.indexingStatus === 'indexing' && (\n                    <Button size=\"sm\" variant=\"outline\" disabled>\n                      <RefreshCw className=\"w-4 h-4 mr-2 animate-spin\" />\n                      Indexing...\n                    </Button>\n                  )}\n                </div>\n              </div>\n            </div>\n            <div className=\"flex gap-2\">\n              <Button\n                variant={activeTab === 'chat' ? 'default' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setActiveTab('chat')}\n              >\n                <Zap className=\"w-4 h-4 mr-2\" />\n                Chat\n              </Button>\n              <Button\n                variant={activeTab === 'stats' ? 'default' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setActiveTab('stats')}\n              >\n                <BarChart3 className=\"w-4 h-4 mr-2\" />\n                Stats\n              </Button>\n              <Button\n                variant={activeTab === 'files' ? 'default' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setActiveTab('files')}\n              >\n                <FileText className=\"w-4 h-4 mr-2\" />\n                Files\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 container mx-auto px-4 py-6\">\n        {/* Indexing Progress */}\n        {isIndexing && (\n          <div className=\"mb-6\">\n            <IndexingProgress\n              projectId={projectId}\n              isIndexing={isIndexing}\n              onComplete={() => {\n                setIsIndexing(false)\n                fetchProject()\n                if (activeTab === 'files') {\n                  fetchFiles()\n                }\n              }}\n            />\n          </div>\n        )}\n\n        {activeTab === 'chat' && (\n          <div className=\"max-w-4xl mx-auto h-full flex flex-col\">\n            {/* Chat Messages */}\n            <div className=\"flex-1 mb-4 space-y-4 overflow-y-auto\">\n              {messages.length === 0 ? (\n                <div className=\"text-center py-12\">\n                  <Code className=\"w-16 h-16 text-muted-foreground mx-auto mb-4\" />\n                  <h3 className=\"text-xl font-semibold mb-2\">Start chatting about your code</h3>\n                  <p className=\"text-muted-foreground\">\n                    Ask questions about functions, classes, or any part of your codebase\n                  </p>\n                </div>\n              ) : (\n                messages.map((message) => (\n                  <div\n                    key={message.id}\n                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}\n                  >\n                    <div\n                      className={`max-w-[80%] rounded-lg px-4 py-2 ${\n                        message.role === 'user'\n                          ? 'bg-primary text-primary-foreground'\n                          : 'bg-muted'\n                      }`}\n                    >\n                      <p className=\"whitespace-pre-wrap\">{message.content}</p>\n                      <p className=\"text-xs opacity-70 mt-1\">\n                        {new Date(message.timestamp).toLocaleTimeString()}\n                      </p>\n                    </div>\n                  </div>\n                ))\n              )}\n              {isSending && (\n                <div className=\"flex justify-start\">\n                  <div className=\"bg-muted rounded-lg px-4 py-2\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-primary\"></div>\n                      <span>AI is thinking...</span>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Chat Input */}\n            <div className=\"flex gap-2\">\n              <Input\n                value={inputMessage}\n                onChange={(e) => setInputMessage(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Ask about your code...\"\n                disabled={isSending}\n                className=\"flex-1\"\n              />\n              <Button onClick={sendMessage} disabled={isSending || !inputMessage.trim()}>\n                <Send className=\"w-4 h-4\" />\n              </Button>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'stats' && (\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Files</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-3xl font-bold\">{project.totalFiles}</div>\n                  <p className=\"text-sm text-muted-foreground\">\n                    {project.indexedFiles} indexed\n                  </p>\n                </CardContent>\n              </Card>\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Lines of Code</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-3xl font-bold\">{project.totalLines.toLocaleString()}</div>\n                  <p className=\"text-sm text-muted-foreground\">Total lines</p>\n                </CardContent>\n              </Card>\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Status</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-lg font-semibold capitalize\">{project.indexingStatus}</div>\n                  <p className=\"text-sm text-muted-foreground\">\n                    {project.lastIndexedAt \n                      ? `Last indexed ${new Date(project.lastIndexedAt).toLocaleDateString()}`\n                      : 'Never indexed'\n                    }\n                  </p>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'files' && (\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <div>\n                <h2 className=\"text-2xl font-bold\">Project Files</h2>\n                <p className=\"text-slate-600 dark:text-slate-400\">\n                  Manage and upload files to your project\n                </p>\n              </div>\n              <div className=\"flex gap-3\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => fetchFiles()}\n                  disabled={isLoadingFiles}\n                >\n                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoadingFiles ? 'animate-spin' : ''}`} />\n                  Refresh\n                </Button>\n                <Button\n                  onClick={() => setIsUploadDialogOpen(true)}\n                  className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\n                >\n                  <Upload className=\"w-4 h-4 mr-2\" />\n                  Upload Files\n                </Button>\n              </div>\n            </div>\n\n            <Card>\n              <CardHeader>\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <CardTitle className=\"flex items-center gap-2\">\n                      <FolderOpen className=\"w-5 h-5\" />\n                      Files ({files.length})\n                    </CardTitle>\n                    <CardDescription>\n                      {files.filter(f => f.isIndexed).length} of {files.length} files indexed\n                    </CardDescription>\n                  </div>\n                  {files.some(f => !f.isIndexed) && (\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => startIndexing(true)}\n                      disabled={isIndexing}\n                    >\n                      {isIndexing ? (\n                        <>\n                          <RefreshCw className=\"w-4 h-4 mr-2 animate-spin\" />\n                          Indexing...\n                        </>\n                      ) : (\n                        <>\n                          <Zap className=\"w-4 h-4 mr-2\" />\n                          Index New Files\n                        </>\n                      )}\n                    </Button>\n                  )}\n                </div>\n              </CardHeader>\n              <CardContent>\n                {isLoadingFiles ? (\n                  <div className=\"space-y-3\">\n                    {[...Array(5)].map((_, i) => (\n                      <div key={i} className=\"animate-pulse\">\n                        <div className=\"h-12 bg-slate-200 dark:bg-slate-700 rounded-lg\"></div>\n                      </div>\n                    ))}\n                  </div>\n                ) : files.length === 0 ? (\n                  <div className=\"text-center py-12\">\n                    <FileText className=\"w-16 h-16 text-slate-400 mx-auto mb-4\" />\n                    <h3 className=\"text-xl font-semibold mb-2\">No files found</h3>\n                    <p className=\"text-slate-600 dark:text-slate-400 mb-6\">\n                      Upload files to get started with your project\n                    </p>\n                    <Button\n                      onClick={() => setIsUploadDialogOpen(true)}\n                      className=\"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\n                    >\n                      <Upload className=\"w-4 h-4 mr-2\" />\n                      Upload Your First Files\n                    </Button>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2\">\n                    {files.map((file) => (\n                      <div\n                        key={file.id}\n                        className=\"flex items-center justify-between p-4 border rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors\"\n                      >\n                        <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                          <div className=\"w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-lg flex items-center justify-center\">\n                            <FileText className=\"w-4 h-4 text-blue-600 dark:text-blue-400\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <p className=\"font-medium truncate\">{file.path}</p>\n                            <div className=\"flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400\">\n                              <span>{(file.size / 1024).toFixed(1)} KB</span>\n                              <span>{file.lines} lines</span>\n                              {file.extension && <span>{file.extension}</span>}\n                            </div>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center gap-3\">\n                          <Badge\n                            variant={file.isIndexed ? 'default' : 'secondary'}\n                            className={file.isIndexed ? 'bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-300' : ''}\n                          >\n                            {file.isIndexed ? 'Indexed' : 'Pending'}\n                          </Badge>\n                          {file.lastIndexedAt && (\n                            <div className=\"flex items-center gap-1 text-xs text-slate-500 dark:text-slate-400\">\n                              <Clock className=\"w-3 h-3\" />\n                              {new Date(file.lastIndexedAt).toLocaleDateString()}\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </div>\n        )}\n      </div>\n\n      {/* File Upload Dialog */}\n      <FileUploadDialog\n        open={isUploadDialogOpen}\n        onOpenChange={setIsUploadDialogOpen}\n        projectId={projectId}\n        onUploadComplete={handleUploadComplete}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAmDO,SAAS,gBAAgB,EAAE,SAAS,EAAwB;;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACpD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;YACA,IAAI,cAAc,SAAS;gBACzB;YACF;QACF;oCAAG;QAAC;QAAW;KAAU;IAEzB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;YACzD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;QACjB,kBAAkB;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,MAAM,CAAC;YAC/D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK,IAAI,EAAE;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAA2B;YAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,KAAK,CAAC,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,SAAS;gBACX;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,mBAAgC;oBACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;oBAC7B,MAAM;oBACN,SAAS,KAAK,QAAQ;oBACtB,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAiB;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,gBAAgB,OAAO,cAAuB,KAAK;QACvD,IAAI,CAAC,WAAW,YAAY;QAE5B,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,MAAM,CAAC,EAAE;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAY;YACrC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,2BAA2B;gBAC3B,MAAM,aAAa,YAAY;oBAC7B,MAAM,iBAAiB,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;oBAC/D,IAAI,eAAe,EAAE,EAAE;wBACrB,MAAM,OAAO,MAAM,eAAe,IAAI;wBACtC,WAAW,KAAK,OAAO;wBAEvB,IAAI,KAAK,OAAO,CAAC,cAAc,KAAK,YAAY;4BAC9C,cAAc;4BACd,cAAc;4BACd,IAAI,cAAc,SAAS;gCACzB,aAAa,+BAA+B;;4BAC9C;wBACF;oBACF;gBACF,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,cAAc;QAChB;IACF;IAEA,MAAM,uBAAuB;QAC3B;QACA,IAAI,cAAc,SAAS;YACzB;QACF;QACA,0CAA0C;QAC1C,WAAW;YACT,cAAc;QAChB,GAAG;IACL;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,SAAM;;;;;0BAEP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI1C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAsB,QAAQ,IAAI;;;;;;0DAChD,6LAAC;gDAAI,WAAU;;oDACZ,QAAQ,QAAQ,kBAAI,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAa,QAAQ,QAAQ;;;;;;oDAChE,QAAQ,SAAS,kBAAI,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW,QAAQ,SAAS;;;;;;kEACjE,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAS,QAAQ,SAAS,GAAG,YAAY;kEAC7C,QAAQ,cAAc,KAAK,aAAa,gBACxC,QAAQ,SAAS,GAAG,YAAY;;;;;;oDAElC,CAAC,QAAQ,SAAS,IAAI,QAAQ,cAAc,KAAK,4BAChD,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS;wDACT,UAAU;;0EAEV,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;oDAIpC,QAAQ,cAAc,KAAK,4BAC1B,6LAAC,qIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAU,QAAQ;;0EAC1C,6LAAC,mNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,SAAS,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,aAAa;;0DAE5B,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,UAAU,YAAY;wCAC7C,MAAK;wCACL,SAAS,IAAM,aAAa;;0DAE5B,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,cAAc,UAAU,YAAY;wCAC7C,MAAK;wCACL,SAAS,IAAM,aAAa;;0DAE5B,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,6LAAC;gBAAI,WAAU;;oBAEZ,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yJAAA,CAAA,mBAAgB;4BACf,WAAW;4BACX,YAAY;4BACZ,YAAY;gCACV,cAAc;gCACd;gCACA,IAAI,cAAc,SAAS;oCACzB;gCACF;4BACF;;;;;;;;;;;oBAKL,cAAc,wBACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;+CAKvC,SAAS,GAAG,CAAC,CAAC,wBACZ,6LAAC;4CAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;sDAE9E,cAAA,6LAAC;gDACC,WAAW,CAAC,iCAAiC,EAC3C,QAAQ,IAAI,KAAK,SACb,uCACA,YACJ;;kEAEF,6LAAC;wDAAE,WAAU;kEAAuB,QAAQ,OAAO;;;;;;kEACnD,6LAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;2CAZ9C,QAAQ,EAAE;;;;;oCAkBpB,2BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCACJ,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,YAAY;wCACZ,aAAY;wCACZ,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS;wCAAa,UAAU,aAAa,CAAC,aAAa,IAAI;kDACrE,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oBAMvB,cAAc,yBACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DAAsB,QAAQ,UAAU;;;;;;8DACvD,6LAAC;oDAAE,WAAU;;wDACV,QAAQ,YAAY;wDAAC;;;;;;;;;;;;;;;;;;;8CAI5B,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DAAsB,QAAQ,UAAU,CAAC,cAAc;;;;;;8DACtE,6LAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAGjD,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;;;;;;sDAEjC,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC;oDAAI,WAAU;8DAAoC,QAAQ,cAAc;;;;;;8DACzE,6LAAC;oDAAE,WAAU;8DACV,QAAQ,aAAa,GAClB,CAAC,aAAa,EAAE,IAAI,KAAK,QAAQ,aAAa,EAAE,kBAAkB,IAAI,GACtE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASf,cAAc,yBACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;;;;;;;kDAIpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM;gDACf,UAAU;;kEAEV,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAW,CAAC,aAAa,EAAE,iBAAiB,iBAAiB,IAAI;;;;;;oDAAI;;;;;;;0DAGlF,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,sBAAsB;gDACrC,WAAU;;kEAEV,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAMzC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAY;gEAC1B,MAAM,MAAM;gEAAC;;;;;;;sEAEvB,6LAAC,mIAAA,CAAA,kBAAe;;gEACb,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;gEAAC;gEAAK,MAAM,MAAM;gEAAC;;;;;;;;;;;;;gDAG5D,MAAM,IAAI,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,mBAC3B,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,cAAc;oDAC7B,UAAU;8DAET,2BACC;;0EACE,6LAAC,mNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAA8B;;qFAIrD;;0EACE,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAQ5C,6LAAC,mIAAA,CAAA,cAAW;kDACT,+BACC,6LAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oDAAY,WAAU;8DACrB,cAAA,6LAAC;wDAAI,WAAU;;;;;;mDADP;;;;;;;;;mDAKZ,MAAM,MAAM,KAAK,kBACnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAG,WAAU;8DAA6B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAA0C;;;;;;8DAGvD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,sBAAsB;oDACrC,WAAU;;sEAEV,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;iEAKvC,6LAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oDAEC,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAwB,KAAK,IAAI;;;;;;sFAC9C,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;;wFAAM,CAAC,KAAK,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;wFAAG;;;;;;;8FACrC,6LAAC;;wFAAM,KAAK,KAAK;wFAAC;;;;;;;gFACjB,KAAK,SAAS,kBAAI,6LAAC;8FAAM,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;sEAI9C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,QAAK;oEACJ,SAAS,KAAK,SAAS,GAAG,YAAY;oEACtC,WAAW,KAAK,SAAS,GAAG,yEAAyE;8EAEpG,KAAK,SAAS,GAAG,YAAY;;;;;;gEAE/B,KAAK,aAAa,kBACjB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,IAAI,KAAK,KAAK,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;;mDA1BjD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAyC9B,6LAAC,6JAAA,CAAA,mBAAgB;gBACf,MAAM;gBACN,cAAc;gBACd,WAAW;gBACX,kBAAkB;;;;;;;;;;;;AAI1B;GA5fgB;KAAA", "debugId": null}}]}