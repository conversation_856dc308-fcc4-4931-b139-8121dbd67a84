'use client'

import { useState, useEffect } from 'react'
import { Plus, Search, Code, Clock, FileText, Zap, Brain, Database, Sparkles, ArrowRight, Github, Star, Users, Rocket } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CreateProjectDialog } from './create-project-dialog'
import { Navbar } from '@/components/navigation/navbar'

interface Project {
  id: string
  name: string
  description?: string
  path: string
  language?: string
  framework?: string
  isIndexed: boolean
  indexingStatus: string
  totalFiles: number
  indexedFiles: number
  totalLines: number
  createdAt: string
  lastIndexedAt?: string
}

export function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchProjects()
  }, [])

  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects')
      if (response.ok) {
        const data = await response.json()
        setProjects(data.projects || [])
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.language?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500'
      case 'indexing': return 'bg-blue-500'
      case 'failed': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Indexed'
      case 'indexing': return 'Indexing...'
      case 'failed': return 'Failed'
      default: return 'Pending'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />

      {/* Content */}
      <div className="relative z-10">
        <Navbar />

        {/* Hero Section */}
        {projects.length === 0 && !isLoading ? (
          <div className="relative overflow-hidden">
            <div className="container mx-auto px-4 py-20">
            <div className="text-center max-w-4xl mx-auto">
              {/* Hero Content */}
              <div className="mb-8 animate-fadeInUp">
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 text-blue-600 dark:text-blue-400 px-6 py-3 rounded-full text-sm font-medium mb-8 border border-blue-200/50 dark:border-blue-800/50 shadow-lg backdrop-blur-sm">
                  <Sparkles className="w-4 h-4" />
                  AI-Powered Code Analysis
                </div>

                <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-purple-900 dark:from-slate-100 dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent mb-8 leading-tight">
                  Understand Your
                  <br />
                  <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Codebase Instantly
                  </span>
                </h1>

                <p className="text-xl text-slate-600 dark:text-slate-300 mb-10 max-w-3xl mx-auto leading-relaxed">
                  Index your code projects and chat with AI to understand functions, classes, and architecture.
                  Get instant insights about your codebase with semantic search and intelligent analysis.
                </p>

                <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 rounded-xl"
                    onClick={() => setIsCreateDialogOpen(true)}
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Create Your First Project
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="px-10 py-4 text-lg font-semibold border-2 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <Github className="w-5 h-5 mr-2" />
                    View on GitHub
                  </Button>
                </div>
              </div>

              {/* Features Grid */}
              <div className="grid md:grid-cols-3 gap-8 mt-20">
                <div className="group bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-3xl p-8 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-2xl hover:border-blue-200 dark:hover:border-blue-800 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Brain className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-slate-900 dark:text-slate-100">AI-Powered Chat</h3>
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                    Ask questions about your code and get intelligent responses from multiple AI providers.
                  </p>
                </div>

                <div className="group bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-3xl p-8 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-2xl hover:border-purple-200 dark:hover:border-purple-800 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Database className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-slate-900 dark:text-slate-100">Smart Indexing</h3>
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                    Automatically index your codebase with vector embeddings for semantic search.
                  </p>
                </div>

                <div className="group bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-3xl p-8 border border-slate-200/50 dark:border-slate-700/50 hover:shadow-2xl hover:border-green-200 dark:hover:border-green-800 transition-all duration-300 hover:-translate-y-2">
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Rocket className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-slate-900 dark:text-slate-100">Multi-Language</h3>
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed">
                    Support for 20+ programming languages including JavaScript, Python, Java, and more.
                  </p>
                </div>
              </div>

              {/* Stats */}
              <div className="flex justify-center items-center gap-8 mt-16 text-sm text-slate-500 dark:text-slate-400">
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4" />
                  <span>Open Source</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>Community Driven</span>
                </div>
                <div className="flex items-center gap-2">
                  <Code className="w-4 h-4" />
                  <span>Developer First</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        ) : (
          /* Projects Section */
          <div className="container mx-auto px-4 py-12">
            {/* Header */}
            <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-12 gap-6">
              <div className="animate-fadeInUp">
                <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-purple-900 dark:from-slate-100 dark:via-blue-100 dark:to-purple-100 bg-clip-text text-transparent mb-3">
                  Your Projects
                </h1>
                <p className="text-xl text-slate-600 dark:text-slate-400">
                  Manage and analyze your code projects with AI
                </p>
              </div>
              <Button
                onClick={() => setIsCreateDialogOpen(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-8 py-3 text-lg font-semibold rounded-xl"
              >
                <Plus className="w-5 h-5 mr-2" />
                New Project
              </Button>
            </div>

            {/* Search */}
            <div className="mb-12">
              <div className="relative max-w-xl mx-auto lg:mx-0">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <Input
                  placeholder="Search projects..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 pr-4 py-4 text-lg bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 rounded-2xl shadow-lg focus:shadow-xl transition-all duration-300 focus:border-blue-300 dark:focus:border-blue-600"
                />
              </div>
            </div>
          </div>
        )}

        {/* Projects Grid */}
        <div className="container mx-auto px-4 pb-12">
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 rounded-3xl p-8">
                  <CardHeader>
                    <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded-xl w-3/4 mb-4"></div>
                    <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded-lg w-full"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded-lg w-1/2"></div>
                      <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded-lg w-2/3"></div>
                      <div className="h-12 bg-slate-200 dark:bg-slate-700 rounded-xl w-full"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredProjects.length === 0 ? (
            <div className="text-center py-20">
              <div className="w-32 h-32 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-xl">
                <Code className="w-16 h-16 text-slate-400" />
              </div>
              <h3 className="text-3xl font-bold mb-4 text-slate-900 dark:text-slate-100">
                {projects.length === 0 ? 'No projects yet' : 'No projects found'}
              </h3>
              <p className="text-xl text-slate-600 dark:text-slate-400 mb-10 max-w-2xl mx-auto leading-relaxed">
                {projects.length === 0
                  ? 'Create your first project to start indexing your code and chatting with AI'
                  : 'Try adjusting your search query to find your projects'
                }
              </p>
              {projects.length === 0 && (
                <Button
                  onClick={() => setIsCreateDialogOpen(true)}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 px-10 py-4 text-lg font-semibold rounded-xl"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Create Your First Project
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredProjects.map((project) => (
                <Card
                  key={project.id}
                  className="group hover:shadow-2xl transition-all duration-500 cursor-pointer bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-slate-200/50 dark:border-slate-700/50 hover:border-blue-300 dark:hover:border-blue-600 hover:-translate-y-2 rounded-3xl overflow-hidden"
                  onClick={() => window.location.href = `/projects/${project.id}`}
                >
                <CardHeader className="pb-4 p-8">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <CardTitle className="text-xl font-bold group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-2">
                        {project.name}
                      </CardTitle>
                      {project.description && (
                        <CardDescription className="text-slate-600 dark:text-slate-400 leading-relaxed">
                          {project.description}
                        </CardDescription>
                      )}
                    </div>
                    <div className="flex items-center gap-2 bg-slate-50 dark:bg-slate-700/50 px-3 py-1 rounded-full">
                      <div className={`w-2 h-2 rounded-full ${getStatusColor(project.indexingStatus)}`} />
                      <span className="text-xs font-medium text-slate-600 dark:text-slate-300">
                        {getStatusText(project.indexingStatus)}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0 px-8 pb-8">
                  <div className="space-y-6">
                    {/* Language and Framework */}
                    <div className="flex gap-3 flex-wrap">
                      {project.language && (
                        <Badge variant="secondary" className="bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 dark:from-blue-900/50 dark:to-blue-800/50 dark:text-blue-300 px-3 py-1 rounded-full font-medium">
                          {project.language}
                        </Badge>
                      )}
                      {project.framework && (
                        <Badge variant="outline" className="border-slate-300 dark:border-slate-600 px-3 py-1 rounded-full font-medium">
                          {project.framework}
                        </Badge>
                      )}
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-slate-50 dark:bg-slate-700/30 rounded-2xl p-4 flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-xl flex items-center justify-center">
                          <FileText className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-slate-900 dark:text-slate-100">
                            {project.indexedFiles}/{project.totalFiles}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">files</div>
                        </div>
                      </div>
                      <div className="bg-slate-50 dark:bg-slate-700/30 rounded-2xl p-4 flex items-center gap-3">
                        <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/50 rounded-xl flex items-center justify-center">
                          <Code className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-slate-900 dark:text-slate-100">
                            {project.totalLines.toLocaleString()}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">lines</div>
                        </div>
                      </div>
                    </div>

                    {/* Last indexed */}
                    {project.lastIndexedAt && (
                      <div className="flex items-center gap-2 text-sm text-slate-500 dark:text-slate-400 bg-slate-50 dark:bg-slate-700/30 rounded-xl px-3 py-2">
                        <Clock className="w-4 h-4" />
                        <span>
                          Indexed {new Date(project.lastIndexedAt).toLocaleDateString()}
                        </span>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex gap-3 pt-4">
                      <Button
                        size="sm"
                        className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 py-3 rounded-xl font-semibold"
                        onClick={(e) => {
                          e.stopPropagation()
                          window.location.href = `/projects/${project.id}`
                        }}
                      >
                        <Zap className="w-4 h-4 mr-2" />
                        Chat with AI
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800 p-3 rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <ArrowRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
              ))}
            </div>
          )}
        </div>

        {/* Create Project Dialog */}
        <CreateProjectDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onProjectCreated={fetchProjects}
        />
      </div>
    </div>
  )
}
