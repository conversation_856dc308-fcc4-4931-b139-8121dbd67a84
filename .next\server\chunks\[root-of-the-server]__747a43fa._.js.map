{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const db =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = db\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,KACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/app/api/projects/%5Bid%5D/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { db } from \"@/lib/db\";\nimport * as fs from \"fs\";\nimport * as path from \"path\";\nimport crypto from \"crypto\";\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params;\n    const project = await db.project.findUnique({\n      where: { id },\n    });\n\n    if (!project) {\n      return NextResponse.json({ error: \"Project not found\" }, { status: 404 });\n    }\n\n    const formData = await request.formData();\n    const files = formData.getAll(\"files\") as File[];\n    const paths = formData.getAll(\"paths\") as string[];\n\n    if (files.length === 0) {\n      return NextResponse.json({ error: \"No files provided\" }, { status: 400 });\n    }\n\n    const uploadedFiles: Array<{\n      path: string;\n      name: string;\n      size: number;\n      status: \"uploaded\" | \"updated\" | \"skipped\";\n    }> = [];\n\n    // Ensure project directory exists\n    const projectDir = project.path;\n    if (!fs.existsSync(projectDir)) {\n      fs.mkdirSync(projectDir, { recursive: true });\n    }\n\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      const relativePath = paths[i] || file.name;\n      const fullPath = path.join(projectDir, relativePath);\n\n      try {\n        // Ensure directory exists\n        const dir = path.dirname(fullPath);\n        if (!fs.existsSync(dir)) {\n          fs.mkdirSync(dir, { recursive: true });\n        }\n\n        // Read file content\n        const arrayBuffer = await file.arrayBuffer();\n        const buffer = Buffer.from(arrayBuffer);\n        const content = buffer.toString(\"utf-8\");\n\n        // Calculate hash for change detection\n        const hash = crypto.createHash(\"md5\").update(content).digest(\"hex\");\n\n        // Check if file already exists with same content\n        let status: \"uploaded\" | \"updated\" | \"skipped\" = \"uploaded\";\n\n        if (fs.existsSync(fullPath)) {\n          const existingContent = fs.readFileSync(fullPath, \"utf-8\");\n          const existingHash = crypto\n            .createHash(\"md5\")\n            .update(existingContent)\n            .digest(\"hex\");\n\n          if (existingHash === hash) {\n            status = \"skipped\";\n          } else {\n            status = \"updated\";\n          }\n        }\n\n        // Write file only if it's new or changed\n        if (status !== \"skipped\") {\n          fs.writeFileSync(fullPath, buffer);\n        }\n\n        // Count lines\n        const lines = content.split(\"\\n\").length;\n\n        // Update database record\n        const extension = path.extname(file.name).toLowerCase();\n\n        await db.projectFile.upsert({\n          where: {\n            projectId_path: {\n              projectId: id,\n              path: relativePath,\n            },\n          },\n          update: {\n            name: file.name,\n            extension,\n            size: file.size,\n            lines,\n            content: file.size < 100000 ? content : null,\n            hash,\n            isIndexed: false, // Mark for re-indexing\n            updatedAt: new Date(),\n          },\n          create: {\n            projectId: id,\n            path: relativePath,\n            name: file.name,\n            extension,\n            size: file.size,\n            lines,\n            content: file.size < 100000 ? content : null,\n            hash,\n            isIndexed: false,\n          },\n        });\n\n        uploadedFiles.push({\n          path: relativePath,\n          name: file.name,\n          size: file.size,\n          status,\n        });\n      } catch (error) {\n        console.error(`Error processing file ${relativePath}:`, error);\n        // Continue with other files\n      }\n    }\n\n    // Update project statistics\n    const totalFiles = await db.projectFile.count({\n      where: { projectId: id },\n    });\n\n    const indexedFiles = await db.projectFile.count({\n      where: {\n        projectId: id,\n        isIndexed: true,\n      },\n    });\n\n    const totalLinesResult = await db.projectFile.aggregate({\n      where: { projectId: id },\n      _sum: { lines: true },\n    });\n\n    await db.project.update({\n      where: { id },\n      data: {\n        totalFiles,\n        indexedFiles,\n        totalLines: totalLinesResult._sum.lines || 0,\n        indexingStatus: \"pending\", // Mark for re-indexing\n        updatedAt: new Date(),\n      },\n    });\n\n    return NextResponse.json({\n      message: \"Files uploaded successfully\",\n      uploadedFiles,\n      stats: {\n        totalFiles,\n        indexedFiles,\n        totalLines: totalLinesResult._sum.lines || 0,\n      },\n    });\n  } catch (error) {\n    console.error(\"Error uploading files:\", error);\n    return NextResponse.json(\n      { error: \"Failed to upload files\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,UAAU,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,QAAQ,SAAS,MAAM,CAAC;QAC9B,MAAM,QAAQ,SAAS,MAAM,CAAC;QAE9B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,MAAM,gBAKD,EAAE;QAEP,kCAAkC;QAClC,MAAM,aAAa,QAAQ,IAAI;QAC/B,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAa,AAAD,EAAE,aAAa;YAC9B,CAAA,GAAA,6FAAA,CAAA,YAAY,AAAD,EAAE,YAAY;gBAAE,WAAW;YAAK;QAC7C;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,eAAe,KAAK,CAAC,EAAE,IAAI,KAAK,IAAI;YAC1C,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAS,AAAD,EAAE,YAAY;YAEvC,IAAI;gBACF,0BAA0B;gBAC1B,MAAM,MAAM,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE;gBACzB,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAa,AAAD,EAAE,MAAM;oBACvB,CAAA,GAAA,6FAAA,CAAA,YAAY,AAAD,EAAE,KAAK;wBAAE,WAAW;oBAAK;gBACtC;gBAEA,oBAAoB;gBACpB,MAAM,cAAc,MAAM,KAAK,WAAW;gBAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;gBAC3B,MAAM,UAAU,OAAO,QAAQ,CAAC;gBAEhC,sCAAsC;gBACtC,MAAM,OAAO,qGAAA,CAAA,UAAM,CAAC,UAAU,CAAC,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC;gBAE7D,iDAAiD;gBACjD,IAAI,SAA6C;gBAEjD,IAAI,CAAA,GAAA,6FAAA,CAAA,aAAa,AAAD,EAAE,WAAW;oBAC3B,MAAM,kBAAkB,CAAA,GAAA,6FAAA,CAAA,eAAe,AAAD,EAAE,UAAU;oBAClD,MAAM,eAAe,qGAAA,CAAA,UAAM,CACxB,UAAU,CAAC,OACX,MAAM,CAAC,iBACP,MAAM,CAAC;oBAEV,IAAI,iBAAiB,MAAM;wBACzB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF;gBAEA,yCAAyC;gBACzC,IAAI,WAAW,WAAW;oBACxB,CAAA,GAAA,6FAAA,CAAA,gBAAgB,AAAD,EAAE,UAAU;gBAC7B;gBAEA,cAAc;gBACd,MAAM,QAAQ,QAAQ,KAAK,CAAC,MAAM,MAAM;gBAExC,yBAAyB;gBACzB,MAAM,YAAY,CAAA,GAAA,iGAAA,CAAA,UAAY,AAAD,EAAE,KAAK,IAAI,EAAE,WAAW;gBAErD,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,MAAM,CAAC;oBAC1B,OAAO;wBACL,gBAAgB;4BACd,WAAW;4BACX,MAAM;wBACR;oBACF;oBACA,QAAQ;wBACN,MAAM,KAAK,IAAI;wBACf;wBACA,MAAM,KAAK,IAAI;wBACf;wBACA,SAAS,KAAK,IAAI,GAAG,SAAS,UAAU;wBACxC;wBACA,WAAW;wBACX,WAAW,IAAI;oBACjB;oBACA,QAAQ;wBACN,WAAW;wBACX,MAAM;wBACN,MAAM,KAAK,IAAI;wBACf;wBACA,MAAM,KAAK,IAAI;wBACf;wBACA,SAAS,KAAK,IAAI,GAAG,SAAS,UAAU;wBACxC;wBACA,WAAW;oBACb;gBACF;gBAEA,cAAc,IAAI,CAAC;oBACjB,MAAM;oBACN,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,aAAa,CAAC,CAAC,EAAE;YACxD,4BAA4B;YAC9B;QACF;QAEA,4BAA4B;QAC5B,MAAM,aAAa,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YAC5C,OAAO;gBAAE,WAAW;YAAG;QACzB;QAEA,MAAM,eAAe,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YAC9C,OAAO;gBACL,WAAW;gBACX,WAAW;YACb;QACF;QAEA,MAAM,mBAAmB,MAAM,kHAAA,CAAA,KAAE,CAAC,WAAW,CAAC,SAAS,CAAC;YACtD,OAAO;gBAAE,WAAW;YAAG;YACvB,MAAM;gBAAE,OAAO;YAAK;QACtB;QAEA,MAAM,kHAAA,CAAA,KAAE,CAAC,OAAO,CAAC,MAAM,CAAC;YACtB,OAAO;gBAAE;YAAG;YACZ,MAAM;gBACJ;gBACA;gBACA,YAAY,iBAAiB,IAAI,CAAC,KAAK,IAAI;gBAC3C,gBAAgB;gBAChB,WAAW,IAAI;YACjB;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA,OAAO;gBACL;gBACA;gBACA,YAAY,iBAAiB,IAAI,CAAC,KAAK,IAAI;YAC7C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}