{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/openai.ts"], "sourcesContent": ["import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from './types'\n\nexport class OpenAIProvider implements AIProvider {\n  name = 'openai'\n  displayName = 'OpenAI'\n  \n  private config: ProviderConfig\n\n  constructor(config: ProviderConfig) {\n    this.config = {\n      model: 'gpt-4',\n      maxTokens: 4000,\n      temperature: 0.7,\n      ...config,\n    }\n  }\n\n  async chat(messages: ChatMessage[], functions?: Function[]): Promise<ChatResponse> {\n    if (!this.isConfigured()) {\n      throw new Error('OpenAI API key not configured')\n    }\n\n    const response = await fetch('https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${this.config.apiKey}`,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        model: this.config.model,\n        messages: messages.map(msg => ({\n          role: msg.role,\n          content: msg.content,\n        })),\n        max_tokens: this.config.maxTokens,\n        temperature: this.config.temperature,\n        functions: functions?.map(fn => ({\n          name: fn.name,\n          description: fn.description,\n          parameters: fn.parameters,\n        })),\n      }),\n    })\n\n    if (!response.ok) {\n      throw new Error(`OpenAI API error: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    const choice = data.choices[0]\n\n    return {\n      content: choice.message.content || '',\n      functionCalls: choice.message.function_call ? [{\n        name: choice.message.function_call.name,\n        arguments: JSON.parse(choice.message.function_call.arguments),\n      }] : undefined,\n      usage: data.usage ? {\n        promptTokens: data.usage.prompt_tokens,\n        completionTokens: data.usage.completion_tokens,\n        totalTokens: data.usage.total_tokens,\n      } : undefined,\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.config.apiKey\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,SAAQ;IACf,cAAc,SAAQ;IAEd,OAAsB;IAE9B,YAAY,MAAsB,CAAE;QAClC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO;YACP,WAAW;YACX,aAAa;YACb,GAAG,MAAM;QACX;IACF;IAEA,MAAM,KAAK,QAAuB,EAAE,SAAsB,EAAyB;QACjF,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,8CAA8C;YACzE,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,UAAU,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC7B,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;gBACjC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;gBACpC,WAAW,WAAW,IAAI,CAAA,KAAM,CAAC;wBAC/B,MAAM,GAAG,IAAI;wBACb,aAAa,GAAG,WAAW;wBAC3B,YAAY,GAAG,UAAU;oBAC3B,CAAC;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,SAAS,UAAU,EAAE;QAC5D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;QAE9B,OAAO;YACL,SAAS,OAAO,OAAO,CAAC,OAAO,IAAI;YACnC,eAAe,OAAO,OAAO,CAAC,aAAa,GAAG;gBAAC;oBAC7C,MAAM,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI;oBACvC,WAAW,KAAK,KAAK,CAAC,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS;gBAC9D;aAAE,GAAG;YACL,OAAO,KAAK,KAAK,GAAG;gBAClB,cAAc,KAAK,KAAK,CAAC,aAAa;gBACtC,kBAAkB,KAAK,KAAK,CAAC,iBAAiB;gBAC9C,aAAa,KAAK,KAAK,CAAC,YAAY;YACtC,IAAI;QACN;IACF;IAEA,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/anthropic.ts"], "sourcesContent": ["import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from './types'\n\nexport class AnthropicProvider implements AIProvider {\n  name = 'anthropic'\n  displayName = 'Anthropic'\n  \n  private config: ProviderConfig\n\n  constructor(config: ProviderConfig) {\n    this.config = {\n      model: 'claude-3-sonnet-20240229',\n      maxTokens: 4000,\n      temperature: 0.7,\n      ...config,\n    }\n  }\n\n  async chat(messages: ChatMessage[], functions?: Function[]): Promise<ChatResponse> {\n    if (!this.isConfigured()) {\n      throw new Error('Anthropic API key not configured')\n    }\n\n    const response = await fetch('https://api.anthropic.com/v1/messages', {\n      method: 'POST',\n      headers: {\n        'x-api-key': this.config.apiKey!,\n        'Content-Type': 'application/json',\n        'anthropic-version': '2023-06-01',\n      },\n      body: JSON.stringify({\n        model: this.config.model,\n        max_tokens: this.config.maxTokens,\n        temperature: this.config.temperature,\n        messages: messages.filter(msg => msg.role !== 'system').map(msg => ({\n          role: msg.role,\n          content: msg.content,\n        })),\n        system: messages.find(msg => msg.role === 'system')?.content,\n      }),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Anthropic API error: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    return {\n      content: data.content[0]?.text || '',\n      usage: data.usage ? {\n        promptTokens: data.usage.input_tokens,\n        completionTokens: data.usage.output_tokens,\n        totalTokens: data.usage.input_tokens + data.usage.output_tokens,\n      } : undefined,\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.config.apiKey\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,YAAW;IAClB,cAAc,YAAW;IAEjB,OAAsB;IAE9B,YAAY,MAAsB,CAAE;QAClC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO;YACP,WAAW;YACX,aAAa;YACb,GAAG,MAAM;QACX;IACF;IAEA,MAAM,KAAK,QAAuB,EAAE,SAAsB,EAAyB;QACjF,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,yCAAyC;YACpE,QAAQ;YACR,SAAS;gBACP,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC/B,gBAAgB;gBAChB,qBAAqB;YACvB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;gBACjC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;gBACpC,UAAU,SAAS,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,UAAU,GAAG,CAAC,CAAA,MAAO,CAAC;wBAClE,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD,QAAQ,SAAS,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,WAAW;YACvD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;QAC/D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO;YACL,SAAS,KAAK,OAAO,CAAC,EAAE,EAAE,QAAQ;YAClC,OAAO,KAAK,KAAK,GAAG;gBAClB,cAAc,KAAK,KAAK,CAAC,YAAY;gBACrC,kBAAkB,KAAK,KAAK,CAAC,aAAa;gBAC1C,aAAa,KAAK,KAAK,CAAC,YAAY,GAAG,KAAK,KAAK,CAAC,aAAa;YACjE,IAAI;QACN;IACF;IAEA,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/google.ts"], "sourcesContent": ["import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from './types'\n\nexport class GoogleProvider implements AIProvider {\n  name = 'google'\n  displayName = 'Google AI'\n  \n  private config: ProviderConfig\n\n  constructor(config: ProviderConfig) {\n    this.config = {\n      model: 'gemini-pro',\n      maxTokens: 4000,\n      temperature: 0.7,\n      baseUrl: 'https://generativelanguage.googleapis.com/v1beta',\n      ...config,\n    }\n  }\n\n  async chat(messages: ChatMessage[], functions?: Function[]): Promise<ChatResponse> {\n    if (!this.isConfigured()) {\n      throw new Error('Google AI API key not configured')\n    }\n\n    // Convert messages to Google's format\n    const contents = messages.map(msg => ({\n      role: msg.role === 'assistant' ? 'model' : 'user',\n      parts: [{ text: msg.content }],\n    }))\n\n    const response = await fetch(\n      `${this.config.baseUrl}/models/${this.config.model}:generateContent?key=${this.config.apiKey}`,\n      {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents,\n          generationConfig: {\n            temperature: this.config.temperature,\n            maxOutputTokens: this.config.maxTokens,\n          },\n        }),\n      }\n    )\n\n    if (!response.ok) {\n      throw new Error(`Google AI API error: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    const candidate = data.candidates?.[0]\n\n    if (!candidate) {\n      throw new Error('No response from Google AI')\n    }\n\n    return {\n      content: candidate.content?.parts?.[0]?.text || '',\n      usage: data.usageMetadata ? {\n        promptTokens: data.usageMetadata.promptTokenCount || 0,\n        completionTokens: data.usageMetadata.candidatesTokenCount || 0,\n        totalTokens: data.usageMetadata.totalTokenCount || 0,\n      } : undefined,\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.config.apiKey\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,SAAQ;IACf,cAAc,YAAW;IAEjB,OAAsB;IAE9B,YAAY,MAAsB,CAAE;QAClC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO;YACP,WAAW;YACX,aAAa;YACb,SAAS;YACT,GAAG,MAAM;QACX;IACF;IAEA,MAAM,KAAK,QAAuB,EAAE,SAAsB,EAAyB;QACjF,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,sCAAsC;QACtC,MAAM,WAAW,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;gBACpC,MAAM,IAAI,IAAI,KAAK,cAAc,UAAU;gBAC3C,OAAO;oBAAC;wBAAE,MAAM,IAAI,OAAO;oBAAC;iBAAE;YAChC,CAAC;QAED,MAAM,WAAW,MAAM,MACrB,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,EAC9F;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,kBAAkB;oBAChB,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;oBACpC,iBAAiB,IAAI,CAAC,MAAM,CAAC,SAAS;gBACxC;YACF;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,UAAU,EAAE;QAC/D;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,YAAY,KAAK,UAAU,EAAE,CAAC,EAAE;QAEtC,IAAI,CAAC,WAAW;YACd,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;YACL,SAAS,UAAU,OAAO,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ;YAChD,OAAO,KAAK,aAAa,GAAG;gBAC1B,cAAc,KAAK,aAAa,CAAC,gBAAgB,IAAI;gBACrD,kBAAkB,KAAK,aAAa,CAAC,oBAAoB,IAAI;gBAC7D,aAAa,KAAK,aAAa,CAAC,eAAe,IAAI;YACrD,IAAI;QACN;IACF;IAEA,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/openrouter.ts"], "sourcesContent": ["import { AIProvider, ChatMessage, ChatResponse, ProviderConfig } from './types'\n\nexport class OpenRouterProvider implements AIProvider {\n  name = 'openrouter'\n  displayName = 'OpenRouter'\n  \n  private config: ProviderConfig\n\n  constructor(config: ProviderConfig) {\n    this.config = {\n      model: 'anthropic/claude-3-sonnet',\n      maxTokens: 4000,\n      temperature: 0.7,\n      baseUrl: 'https://openrouter.ai/api/v1',\n      ...config,\n    }\n  }\n\n  async chat(messages: ChatMessage[], functions?: Function[]): Promise<ChatResponse> {\n    if (!this.isConfigured()) {\n      throw new Error('OpenRouter API key not configured')\n    }\n\n    const response = await fetch(`${this.config.baseUrl}/chat/completions`, {\n      method: 'POST',\n      headers: {\n        'Authorization': `Bearer ${this.config.apiKey}`,\n        'Content-Type': 'application/json',\n        'HTTP-Referer': 'http://localhost:3000',\n        'X-Title': 'Code Index',\n      },\n      body: JSON.stringify({\n        model: this.config.model,\n        messages: messages.map(msg => ({\n          role: msg.role,\n          content: msg.content,\n        })),\n        max_tokens: this.config.maxTokens,\n        temperature: this.config.temperature,\n        functions: functions?.map(fn => ({\n          name: fn.name,\n          description: fn.description,\n          parameters: fn.parameters,\n        })),\n      }),\n    })\n\n    if (!response.ok) {\n      throw new Error(`OpenRouter API error: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    const choice = data.choices[0]\n\n    return {\n      content: choice.message.content || '',\n      functionCalls: choice.message.function_call ? [{\n        name: choice.message.function_call.name,\n        arguments: JSON.parse(choice.message.function_call.arguments),\n      }] : undefined,\n      usage: data.usage ? {\n        promptTokens: data.usage.prompt_tokens,\n        completionTokens: data.usage.completion_tokens,\n        totalTokens: data.usage.total_tokens,\n      } : undefined,\n    }\n  }\n\n  isConfigured(): boolean {\n    return !!this.config.apiKey\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,aAAY;IACnB,cAAc,aAAY;IAElB,OAAsB;IAE9B,YAAY,MAAsB,CAAE;QAClC,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO;YACP,WAAW;YACX,aAAa;YACb,SAAS;YACT,GAAG,MAAM;QACX;IACF;IAEA,MAAM,KAAK,QAAuB,EAAE,SAAsB,EAAyB;QACjF,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACtE,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBAC/C,gBAAgB;gBAChB,gBAAgB;gBAChB,WAAW;YACb;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,UAAU,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC7B,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD,YAAY,IAAI,CAAC,MAAM,CAAC,SAAS;gBACjC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;gBACpC,WAAW,WAAW,IAAI,CAAA,KAAM,CAAC;wBAC/B,MAAM,GAAG,IAAI;wBACb,aAAa,GAAG,WAAW;wBAC3B,YAAY,GAAG,UAAU;oBAC3B,CAAC;YACH;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAChE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,SAAS,KAAK,OAAO,CAAC,EAAE;QAE9B,OAAO;YACL,SAAS,OAAO,OAAO,CAAC,OAAO,IAAI;YACnC,eAAe,OAAO,OAAO,CAAC,aAAa,GAAG;gBAAC;oBAC7C,MAAM,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI;oBACvC,WAAW,KAAK,KAAK,CAAC,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS;gBAC9D;aAAE,GAAG;YACL,OAAO,KAAK,KAAK,GAAG;gBAClB,cAAc,KAAK,KAAK,CAAC,aAAa;gBACtC,kBAAkB,KAAK,KAAK,CAAC,iBAAiB;gBAC9C,aAAa,KAAK,KAAK,CAAC,YAAY;YACtC,IAAI;QACN;IACF;IAEA,eAAwB;QACtB,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/lib/ai-providers/index.ts"], "sourcesContent": ["import { OpenAIProvider } from \"./openai\";\nimport { AnthropicProvider } from \"./anthropic\";\nimport { GoogleProvider } from \"./google\";\nimport { OpenRouterProvider } from \"./openrouter\";\nimport { AIProvider, ProviderConfig } from \"./types\";\n\nexport * from \"./types\";\n\nexport function createProvider(\n  name: string,\n  config: ProviderConfig\n): AIProvider {\n  switch (name) {\n    case \"openai\":\n      return new OpenAIProvider(config);\n    case \"anthropic\":\n      return new AnthropicProvider(config);\n    case \"google\":\n      return new GoogleProvider(config);\n    case \"openrouter\":\n      return new OpenRouterProvider(config);\n    default:\n      throw new Error(`Unknown provider: ${name}`);\n  }\n}\n\nexport function getAvailableProviders(): Array<{\n  name: string;\n  displayName: string;\n}> {\n  return [\n    { name: \"openai\", displayName: \"OpenAI\" },\n    { name: \"anthropic\", displayName: \"Anthropic\" },\n    { name: \"google\", displayName: \"Google AI\" },\n    { name: \"openrouter\", displayName: \"OpenRouter\" },\n  ];\n}\n\nexport function getConfiguredProviders(): Array<{\n  name: string;\n  displayName: string;\n  isConfigured: boolean;\n}> {\n  const providers = getAvailableProviders();\n\n  return providers.map((provider) => {\n    let isConfigured = false;\n\n    try {\n      const config = getProviderConfig(provider.name);\n      const providerInstance = createProvider(provider.name, config);\n      isConfigured = providerInstance.isConfigured();\n    } catch (error) {\n      // Provider not configured\n    }\n\n    return {\n      ...provider,\n      isConfigured,\n    };\n  });\n}\n\nexport function getProviderConfig(name: string): ProviderConfig {\n  switch (name) {\n    case \"openai\":\n      return {\n        apiKey: process.env.OPENAI_API_KEY,\n      };\n    case \"anthropic\":\n      return {\n        apiKey: process.env.ANTHROPIC_API_KEY,\n      };\n    case \"google\":\n      return {\n        apiKey: process.env.GOOGLE_API_KEY,\n      };\n    case \"openrouter\":\n      return {\n        apiKey: process.env.OPENROUTER_API_KEY,\n      };\n    default:\n      throw new Error(`Unknown provider: ${name}`);\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAGA;;;;;;AAEO,SAAS,eACd,IAAY,EACZ,MAAsB;IAEtB,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,yIAAA,CAAA,iBAAc,CAAC;QAC5B,KAAK;YACH,OAAO,IAAI,4IAAA,CAAA,oBAAiB,CAAC;QAC/B,KAAK;YACH,OAAO,IAAI,yIAAA,CAAA,iBAAc,CAAC;QAC5B,KAAK;YACH,OAAO,IAAI,6IAAA,CAAA,qBAAkB,CAAC;QAChC;YACE,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM;IAC/C;AACF;AAEO,SAAS;IAId,OAAO;QACL;YAAE,MAAM;YAAU,aAAa;QAAS;QACxC;YAAE,MAAM;YAAa,aAAa;QAAY;QAC9C;YAAE,MAAM;YAAU,aAAa;QAAY;QAC3C;YAAE,MAAM;YAAc,aAAa;QAAa;KACjD;AACH;AAEO,SAAS;IAKd,MAAM,YAAY;IAElB,OAAO,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,eAAe;QAEnB,IAAI;YACF,MAAM,SAAS,kBAAkB,SAAS,IAAI;YAC9C,MAAM,mBAAmB,eAAe,SAAS,IAAI,EAAE;YACvD,eAAe,iBAAiB,YAAY;QAC9C,EAAE,OAAO,OAAO;QACd,0BAA0B;QAC5B;QAEA,OAAO;YACL,GAAG,QAAQ;YACX;QACF;IACF;AACF;AAEO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,QAAQ,QAAQ,GAAG,CAAC,cAAc;YACpC;QACF,KAAK;YACH,OAAO;gBACL,QAAQ,QAAQ,GAAG,CAAC,iBAAiB;YACvC;QACF,KAAK;YACH,OAAO;gBACL,QAAQ,QAAQ,GAAG,CAAC,cAAc;YACpC;QACF,KAAK;YACH,OAAO;gBACL,QAAQ,QAAQ,GAAG,CAAC,kBAAkB;YACxC;QACF;YACE,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Projekte/code-index/src/app/api/settings/providers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getConfiguredProviders } from '@/lib/ai-providers'\n\nexport async function GET() {\n  try {\n    const providers = getConfiguredProviders()\n    \n    return NextResponse.json({ providers })\n  } catch (error) {\n    console.error('Error fetching providers:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch providers' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { apiKeys } = body\n\n    // In a real application, you would save these to a secure database\n    // For this demo, we'll just return success\n    // The API keys should be stored encrypted and associated with the user\n\n    console.log('API keys would be saved:', Object.keys(apiKeys))\n\n    return NextResponse.json({ \n      success: true,\n      message: 'Settings saved successfully'\n    })\n  } catch (error) {\n    console.error('Error saving settings:', error)\n    return NextResponse.json(\n      { error: 'Failed to save settings' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD;QAEvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAU;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,GAAG;QAEpB,mEAAmE;QACnE,2CAA2C;QAC3C,uEAAuE;QAEvE,QAAQ,GAAG,CAAC,4BAA4B,OAAO,IAAI,CAAC;QAEpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}