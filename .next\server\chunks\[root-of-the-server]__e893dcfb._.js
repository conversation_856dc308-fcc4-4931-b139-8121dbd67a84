module.exports = {

"[project]/.next-internal/server/app/api/settings/models/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/settings/models/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function GET(request) {
    try {
        // Fetch models from OpenRouter
        const response = await fetch('https://openrouter.ai/api/v1/models', {
            headers: {
                'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY || ''}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error('Failed to fetch models from OpenRouter');
        }
        const data = await response.json();
        const models = data.data || [];
        // Filter and format models
        const formattedModels = models.filter((model)=>{
            // Filter for popular and free models
            return model.id.includes('free') || model.id.includes('qwen') || model.id.includes('llama') || model.id.includes('mistral') || model.id.includes('claude') || model.id.includes('gpt');
        }).map((model)=>({
                id: model.id,
                name: model.name,
                description: model.description || '',
                pricing: {
                    prompt: model.pricing.prompt,
                    completion: model.pricing.completion
                },
                contextLength: model.context_length,
                isFree: model.pricing.prompt === '0' && model.pricing.completion === '0',
                provider: 'openrouter'
            })).sort((a, b)=>{
            // Sort free models first, then by name
            if (a.isFree && !b.isFree) return -1;
            if (!a.isFree && b.isFree) return 1;
            return a.name.localeCompare(b.name);
        });
        // Add some popular models from other providers
        const otherModels = [
            {
                id: 'gpt-4o',
                name: 'GPT-4o',
                description: 'Latest GPT-4 model with improved capabilities',
                pricing: {
                    prompt: '0.005',
                    completion: '0.015'
                },
                contextLength: 128000,
                isFree: false,
                provider: 'openai'
            },
            {
                id: 'gpt-4o-mini',
                name: 'GPT-4o Mini',
                description: 'Faster and cheaper GPT-4 variant',
                pricing: {
                    prompt: '0.00015',
                    completion: '0.0006'
                },
                contextLength: 128000,
                isFree: false,
                provider: 'openai'
            },
            {
                id: 'claude-3-5-sonnet-20241022',
                name: 'Claude 3.5 Sonnet',
                description: 'Latest Claude model with excellent reasoning',
                pricing: {
                    prompt: '0.003',
                    completion: '0.015'
                },
                contextLength: 200000,
                isFree: false,
                provider: 'anthropic'
            },
            {
                id: 'gemini-1.5-pro',
                name: 'Gemini 1.5 Pro',
                description: 'Google\'s most capable model',
                pricing: {
                    prompt: '0.00125',
                    completion: '0.005'
                },
                contextLength: 2000000,
                isFree: false,
                provider: 'google'
            }
        ];
        const allModels = [
            ...formattedModels,
            ...otherModels
        ];
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            models: allModels,
            totalCount: allModels.length
        });
    } catch (error) {
        console.error('Error fetching models:', error);
        // Return fallback models if API fails
        const fallbackModels = [
            {
                id: 'qwen/qwen-2.5-72b-instruct:free',
                name: 'Qwen 2.5 72B Instruct (Free)',
                description: 'High-quality free model from Alibaba',
                pricing: {
                    prompt: '0',
                    completion: '0'
                },
                contextLength: 32768,
                isFree: true,
                provider: 'openrouter'
            },
            {
                id: 'meta-llama/llama-3.1-8b-instruct:free',
                name: 'Llama 3.1 8B Instruct (Free)',
                description: 'Meta\'s open-source model',
                pricing: {
                    prompt: '0',
                    completion: '0'
                },
                contextLength: 131072,
                isFree: true,
                provider: 'openrouter'
            },
            {
                id: 'gpt-4o-mini',
                name: 'GPT-4o Mini',
                description: 'OpenAI\'s efficient model',
                pricing: {
                    prompt: '0.00015',
                    completion: '0.0006'
                },
                contextLength: 128000,
                isFree: false,
                provider: 'openai'
            }
        ];
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            models: fallbackModels,
            totalCount: fallbackModels.length,
            error: 'Failed to fetch latest models, showing fallback list'
        });
    }
}
async function POST(request) {
    try {
        const { selectedModel, provider } = await request.json();
        // Here you would save the selected model to your database or settings
        // For now, we'll just return success
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Model selection saved successfully',
            selectedModel,
            provider
        });
    } catch (error) {
        console.error('Error saving model selection:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to save model selection'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e893dcfb._.js.map